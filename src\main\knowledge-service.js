const { app } = require('electron')
const path = require('path')
const { getCurrentUserToken, createMainApiClient } = require('./api-client')
const { getKnowledgeConfig } = require('./utils')

// === 知识库服务 - 直接在主进程中定义 ===

// 延迟导入，避免启动时的依赖问题
let createClient = null
let OpenAI = null
let mammoth = null
let TurndownService = null

// 知识库全局变量
let libsqlClient = null
let openaiClient = null
let isKnowledgeInitialized = false

/**
 * 初始化知识库依赖库
 */
async function initializeKnowledgeDependencies() {
  try {
    console.log('🔧 初始化知识库依赖...')

    // 动态导入依赖
    const libsql = await import('@libsql/client')
    createClient = libsql.createClient

    const openaiModule = await import('openai')
    OpenAI = openaiModule.default

    const mammothModule = await import('mammoth')
    mammoth = mammothModule.default

    const turndownModule = await import('turndown')
    TurndownService = turndownModule.default

    // 设置数据库路径（用户数据目录）
    const userDataPath = app.getPath('userData')
    const dbPath = path.join(userDataPath, 'knowledge.db')
    const KNOWLEDGE_CONFIG = getKnowledgeConfig()
    KNOWLEDGE_CONFIG.database.url = `file:${dbPath}`

    console.log('📂 知识库数据库路径:', dbPath)

    // 创建客户端
    libsqlClient = createClient({
      url: KNOWLEDGE_CONFIG.database.url,
      timeout: KNOWLEDGE_CONFIG.database.timeout
    })

    openaiClient = new OpenAI({
      baseURL: KNOWLEDGE_CONFIG.embedding.baseURL,
      apiKey: KNOWLEDGE_CONFIG.embedding.apiKey
    })

    console.log('✅ 知识库依赖初始化成功')
    return true
  } catch (error) {
    console.error('❌ 知识库依赖初始化失败:', error)
    return false
  }
}

/**
 * 初始化知识库数据库
 */
async function initKnowledgeDatabase() {
  try {
    if (!libsqlClient) {
      const success = await initializeKnowledgeDependencies()
      if (!success) {
        throw new Error('依赖初始化失败')
      }
    }

    console.log('📊 初始化知识库数据库...')

    // 检查表是否已存在，避免重复创建
    console.log('🗂️ 检查数据库表结构...')

    try {
      // 检查表是否存在
      const tablesResult = await libsqlClient.execute(`
        SELECT name FROM sqlite_master 
        WHERE type='table' AND name IN ('user_file', 'user_file_embd')
      `)

      const existingTables = tablesResult.rows.map(row => row.name)
      console.log('🗂️ 现有表:', existingTables)

      // 只有当表不存在时才创建
      if (!existingTables.includes('user_file') || !existingTables.includes('user_file_embd')) {
        console.log('🗂️ 创建缺失的数据库表结构...')

        // 创建新的表结构
        await libsqlClient.batch([
          `CREATE TABLE IF NOT EXISTS user_file (
            id INTEGER PRIMARY KEY,
            file_name TEXT NOT NULL,
            file_path TEXT NOT NULL,
            source_file_path TEXT NOT NULL,
            file_preview TEXT NOT NULL,
            remark TEXT NOT NULL,
            file_size INTEGER DEFAULT 0,
            create_time DATETIME DEFAULT CURRENT_TIMESTAMP
          )`,
          `CREATE TABLE IF NOT EXISTS user_file_embd (
            id INTEGER PRIMARY KEY,
            file_id INTEGER NOT NULL,
            file_content TEXT NOT NULL,
            embedding F32_BLOB(1024),
            create_time DATETIME DEFAULT CURRENT_TIMESTAMP
          )`
        ], 'write')

        console.log('🗂️ 创建表结构完成，正在创建向量索引...')

        // 单独创建向量索引，并添加错误处理
        try {
          await libsqlClient.execute(
            'CREATE INDEX IF NOT EXISTS file_embedding_idx ON user_file_embd(libsql_vector_idx(embedding))'
          )
          console.log('✅ 向量索引创建成功')
        } catch (indexError) {
          console.error('⚠️ 向量索引创建失败:', indexError.message)
          console.log('🔄 尝试不使用向量索引继续运行...')
          // 不抛出错误，允许在没有向量索引的情况下继续运行
        }
      } else {
        console.log('✅ 数据库表结构已存在，跳过创建')
      }
    } catch (error) {
      console.error('❌ 检查表结构时出错:', error.message)
      // 如果检查失败，尝试创建表
      console.log('🔄 尝试创建表结构...')

      await libsqlClient.batch([
        `CREATE TABLE IF NOT EXISTS user_file (
          id INTEGER PRIMARY KEY,
          file_name TEXT NOT NULL,
          file_path TEXT NOT NULL,
          source_file_path TEXT NOT NULL,
          file_preview TEXT NOT NULL,
          remark TEXT NOT NULL,
          file_size INTEGER DEFAULT 0,
          create_time DATETIME DEFAULT CURRENT_TIMESTAMP
        )`,
        `CREATE TABLE IF NOT EXISTS user_file_embd (
          id INTEGER PRIMARY KEY,
          file_id INTEGER NOT NULL,
          file_content TEXT NOT NULL,
          embedding F32_BLOB(1024),
          create_time DATETIME DEFAULT CURRENT_TIMESTAMP
        )`
      ], 'write')

      try {
        await libsqlClient.execute(
          'CREATE INDEX IF NOT EXISTS file_embedding_idx ON user_file_embd(libsql_vector_idx(embedding))'
        )
      } catch (indexError) {
        console.warn('⚠️ 向量索引创建失败，继续运行:', indexError.message)
      }
    }

    // 检查是否需要添加文件大小字段
    try {
      const columnCheckResult = await libsqlClient.execute(
        "PRAGMA table_info(user_file)"
      )
      
      const hasFileSizeColumn = columnCheckResult.rows.some(row => row.name === 'file_size')
      
      if (!hasFileSizeColumn) {
        console.log('🔄 检测到数据库需要迁移，添加file_size字段...')
        try {
          await libsqlClient.execute('ALTER TABLE user_file ADD COLUMN file_size INTEGER DEFAULT 0')
          console.log('✅ 数据库迁移完成，已添加file_size字段')
        } catch (alterError) {
          console.warn('⚠️ ALTER TABLE失败，尝试重建表结构:', alterError.message)
          
          // 如果ALTER TABLE失败，尝试重建表
          await libsqlClient.execute('DROP TABLE IF EXISTS user_file_embd')
          await libsqlClient.execute('DROP TABLE IF EXISTS user_file')
          
          // 重新创建表结构
          await libsqlClient.batch([
            `CREATE TABLE user_file (
              id INTEGER PRIMARY KEY,
              file_name TEXT NOT NULL,
              file_path TEXT NOT NULL,
              source_file_path TEXT NOT NULL,
              file_preview TEXT NOT NULL,
              remark TEXT NOT NULL,
              file_size INTEGER DEFAULT 0,
              create_time DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,
            `CREATE TABLE user_file_embd (
              id INTEGER PRIMARY KEY,
              file_id INTEGER NOT NULL,
              file_content TEXT NOT NULL,
              embedding F32_BLOB(1024),
              create_time DATETIME DEFAULT CURRENT_TIMESTAMP
            )`
          ], 'write')
          
          console.log('✅ 表结构重建完成，已包含file_size字段')
          
          // 重新创建向量索引
          try {
            await libsqlClient.execute(
              'CREATE INDEX IF NOT EXISTS file_embedding_idx ON user_file_embd(libsql_vector_idx(embedding))'
            )
            console.log('✅ 向量索引重新创建成功')
          } catch (indexError) {
            console.warn('⚠️ 向量索引重新创建失败:', indexError.message)
          }
        }
      } else {
        console.log('✅ file_size字段已存在')
      }
    } catch (migrationError) {
      console.warn('⚠️ 数据库迁移检查失败:', migrationError.message)
    }

    isKnowledgeInitialized = true
    console.log('✅ 知识库数据库初始化成功')
    return { success: true }
  } catch (error) {
    console.error('❌ 知识库数据库初始化失败:', error)
    return { success: false, error: error.message }
  }
}

/**
 * 获取文本的向量表示（使用代理）
 */
async function getKnowledgeEmbedding(text) {
  try {
    const KNOWLEDGE_CONFIG = getKnowledgeConfig()
    // 检查文本长度，如果过长则截断
    let processedText = text
    if (text.length > KNOWLEDGE_CONFIG.embedding.maxTokens) {
      console.log(`⚠️ 文本过长 (${text.length} 字符)，截断到 ${KNOWLEDGE_CONFIG.embedding.maxTokens} 字符`)
      processedText = text.substring(0, KNOWLEDGE_CONFIG.embedding.maxTokens)
    }

    console.log(`🔗 正在通过代理向量化文本，长度: ${processedText.length} 字符`)

    // 获取用户token并创建API客户端
    const userToken = await getCurrentUserToken()
    console.log('🔑 向量化请求使用的token:', userToken ? userToken.substring(0, 20) + '...' : '无token')
    const client = createMainApiClient(null, userToken)

    const requestData = {
      model: KNOWLEDGE_CONFIG.embedding.model,
      input: processedText,
      encoding_format: KNOWLEDGE_CONFIG.embedding.encoding_format
    }

    const response = await client.post('/embeddings', requestData)

    // 打印完整的响应信息用于调试
    console.log('🔍 完整响应对象:', {
      status: response.status,
      statusText: response.statusText,
      headers: response.headers,
      data: response.data
    })

    // 处理嵌套的响应结构
    const responseData = response.data.data || response.data
    console.log('🔍 响应数据结构:', {
      hasResponseData: !!responseData,
      responseDataType: typeof responseData,
      isArray: Array.isArray(responseData),
      hasData: responseData && responseData.data,
      dataLength: responseData && responseData.data ? responseData.data.length : 0
    })

    // 根据实际响应格式解析数据
    let embeddingData
    if (responseData && responseData.data && Array.isArray(responseData.data)) {
      // 新格式：response.data.data.data[0].embedding
      embeddingData = responseData.data[0]
    } else if (responseData && Array.isArray(responseData)) {
      // 旧格式：response.data.data[0].embedding
      embeddingData = responseData[0]
    } else {
      throw new Error('代理返回的向量数据格式不正确')
    }

    if (!embeddingData || !embeddingData.embedding) {
      throw new Error('代理返回的向量数据格式不正确')
    }

    const embedding = new Float32Array(embeddingData.embedding)
    console.log(`✅ 代理向量化成功，维度: ${embedding.length}`)
    return embedding
  } catch (error) {
    console.error('❌ 代理向量化失败:', error)
    console.error('❌ 输入文本长度:', text.length)
    console.error('❌ 错误详情:', error.message)
    throw error
  }
}

// 使用重排序模型并过滤相似度低的片段（使用代理）
async function rerank(similarChunks, queryText) {
  try {
    const documents = similarChunks.map(chunk => chunk.content);

    console.log('🔄 正在通过代理重排序文档片段:', {
      documentsCount: documents.length,
      queryLength: queryText.length
    });

    // 获取用户token并创建API客户端
    const userToken = await getCurrentUserToken()
    console.log('🔑 重排序请求使用的token:', userToken ? userToken.substring(0, 20) + '...' : '无token')
    const client = createMainApiClient(null, userToken)

    const requestData = {
      query: queryText,
      documents: documents,
      model: "BAAI/bge-reranker-v2-m3"
    };

    const response = await client.post('/rerank', requestData);

    // 打印完整的响应信息用于调试
    console.log('🔍 重排序完整响应对象:', {
      status: response.status,
      statusText: response.statusText,
      headers: response.headers,
      data: response.data
    })

    // 处理嵌套的响应结构
    const responseData = response.data.data || response.data;
    console.log('🔍 重排序响应数据结构:', {
      hasResponseData: !!responseData,
      responseDataType: typeof responseData,
      hasResults: responseData && responseData.results,
      resultsLength: responseData && responseData.results ? responseData.results.length : 0
    })

    if (responseData && responseData.results) {
      console.log(`✅ 代理重排序成功，返回 ${responseData.results.length} 个结果`);
      return getTopChunks(responseData, similarChunks);
    }
  } catch (err) {
    console.error('❌ 代理重排序失败:', err);
  }
  return similarChunks;
}

// 动态阈值筛选优质片段
async function getTopChunks(response, chunks, topN = 4, minScore = 0.3) {
  // 提取并排序结果（按相关性分数降序）
  const sortedResults = response.results
    .slice() // 创建副本避免修改原数组
    .sort((a, b) => b.relevance_score - a.relevance_score);

  // 计算统计指标
  const scores = sortedResults.map(res => res.relevance_score);
  const mean = scores.reduce((sum, val) => sum + val, 0) / scores.length;
  const stdDev = Math.sqrt(
    scores.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / scores.length
  );

  // 改进的动态阈值计算
  // 使用更宽松的阈值：均值减去0.5个标准差，或者直接使用前N个结果
  const dynamicThreshold = Math.max(minScore, mean - 0.5 * stdDev);
  const finalThreshold = Math.min(dynamicThreshold, 0.05); // 设置上限，避免过于严格

  console.log(`📊 重排序统计: 均值=${mean.toFixed(3)}, 标准差=${stdDev.toFixed(3)}, 动态阈值=${finalThreshold.toFixed(3)}`);
  console.log(`📊 相关性分数分布:`, scores.map(s => s.toFixed(3)));

  // 如果动态阈值过滤掉太多结果，则直接返回前N个
  const filteredResults = sortedResults.filter(res => res.relevance_score >= finalThreshold);

  if (filteredResults.length === 0) {
    console.log(`⚠️ 动态阈值过滤掉所有结果，返回前${topN}个结果`);
    const indexList = sortedResults.slice(0, topN).map(res => res.index);
    return chunks.filter((chunk, index) => indexList.includes(index));
  }

  console.log(`📊 阈值过滤后剩余: ${filteredResults.length} 个结果`);

  // 筛选满足条件的chunks
  const indexList = filteredResults
    .slice(0, topN) // 限制最大返回数量
    .map(res => res.index);

  return chunks.filter((chunk, index) => indexList.includes(index));
}

// 根据文件类型查询相似片段
async function findSimilarChunks(description, fileType = null, limit = 10) {
  const queryEmbedding = await getKnowledgeEmbedding(description);

  let sql, args;

  if (fileType) {
    // 查询指定fileType的file
    const files = await libsqlClient.execute({
      sql: `SELECT id FROM user_file WHERE file_type = ?`,
      args: [fileType]
    });

    if (!files.rows.length) {
      return [];
    }

    const fileIds = files.rows.map(row => row.id);

    sql = `WITH vector_scores AS (
      SELECT rowid AS id,
             file_id,
             file_content,
             embedding,
             1 - vector_distance_cos(embedding, vector32(?)) AS similarity
      FROM user_file_embd
      WHERE file_id IN (${Array(fileIds.length).fill('?').join(',')})
      ORDER BY similarity DESC
      LIMIT ?
    )
    SELECT v.id,
           v.file_id AS fileId,
           v.file_content AS content,
           v.similarity,
           f.file_name,
           f.source_file_path AS filePath
    FROM vector_scores v
    LEFT JOIN user_file f ON v.file_id = f.id`;

    args = [JSON.stringify(Array.from(queryEmbedding)), ...fileIds, limit];
  } else {
    // 查询所有文件
    sql = `WITH vector_scores AS (
      SELECT rowid AS id,
             file_id,
             file_content,
             embedding,
             1 - vector_distance_cos(embedding, vector32(?)) AS similarity
      FROM user_file_embd
      ORDER BY similarity DESC
      LIMIT ?
    )
    SELECT v.id,
           v.file_id AS fileId,
           v.file_content AS content,
           v.similarity,
           f.file_name,
           f.source_file_path AS filePath
    FROM vector_scores v
    LEFT JOIN user_file f ON v.file_id = f.id`;

    args = [JSON.stringify(Array.from(queryEmbedding)), limit];
  }

  const results = await libsqlClient.execute({ sql, args });
  return results.rows;
}

/**
 * 搜索知识库 - 增强版本，支持重排序和动态阈值
 */
async function searchKnowledge(query, limit = null, fileType = null) {
  try {
    // 只有在数据库还未初始化时才进行初始化
    if (!isKnowledgeInitialized) {
      console.log('🔧 知识库未初始化，开始初始化...')
      await initKnowledgeDatabase()
    }

    const KNOWLEDGE_CONFIG = getKnowledgeConfig()
    const searchLimit = limit || KNOWLEDGE_CONFIG.search.defaultLimit

    console.log(`🔍 搜索知识库: "${query}"${fileType ? ` (文件类型: ${fileType})` : ''}`)

    // 首先检查数据库中是否有数据
    const countResult = await libsqlClient.execute('SELECT COUNT(*) as count FROM user_file_embd')
    const totalEmbeddings = countResult.rows[0].count
    console.log(`📊 数据库中共有 ${totalEmbeddings} 个embedding片段`)

    if (totalEmbeddings === 0) {
      console.log('⚠️ 数据库中没有embedding数据，请先索引文档')
      return []
    }

    // 尝试使用增强搜索
    let searchResults = []
    let useVectorSearch = true

    try {
      // 第一步：使用向量搜索找到候选结果
      console.log('🔍 第一步：向量搜索候选结果...')
      let similarChunks = await findSimilarChunks(query, fileType, searchLimit * 2)

      console.log(`🔍 向量搜索结果: ${similarChunks.length} 个候选`)

      if (similarChunks.length === 0) {
        console.log('⚠️ 向量搜索没有找到结果')
        return []
      }

      // 第二步：使用重排序模型优化结果
      console.log('🔄 第二步：重排序优化结果...')
      const rerankedChunks = await rerank(similarChunks, query)

      console.log(`✨ 重排序后结果: ${rerankedChunks.length} 个`)

      // 转换为标准格式
      searchResults = rerankedChunks.map(row => ({
        id: row.id,
        file_id: row.fileId || row.file_id,
        content: row.content,
        similarity: row.similarity,
        file_name: row.file_name,
        source_file_path: row.filePath || row.source_file_path
      }))

    } catch (vectorError) {
      console.error('⚠️ 增强搜索失败，降级到简单文本搜索:', vectorError.message)
      useVectorSearch = false

      // 降级到基于文本的简单搜索
      try {
        const textSearchResults = await libsqlClient.execute({
          sql: `
            SELECT rowid as id,
                   file_id,
                   file_content as content,
                   0.5 as similarity
            FROM user_file_embd
            WHERE file_content LIKE ? OR file_content LIKE ?
            ORDER BY
              CASE
                WHEN file_content LIKE ? THEN 1
                WHEN file_content LIKE ? THEN 2
                ELSE 3
              END
            LIMIT ?
          `,
          args: [
            `%${query}%`,
            `%${query.split(' ').join('%')}%`,
            `%${query}%`,
            `%${query.split(' ').join('%')}%`,
            searchLimit
          ]
        })

        searchResults = textSearchResults.rows.map(row => ({
          id: row.id,
          file_id: row.file_id,
          content: row.content,
          similarity: row.similarity
        }))

        console.log(`📝 文本搜索结果: ${searchResults.length} 个`)
      } catch (textError) {
        console.error('❌ 文本搜索也失败:', textError.message)
        searchResults = []
      }
    }

    if (useVectorSearch) {
      console.log(`✅ 使用增强向量搜索，最终返回 ${searchResults.length} 个相关文档片段`)
    } else {
      console.log(`✅ 使用文本搜索，最终返回 ${searchResults.length} 个相关文档片段`)
    }

    // 应用最终相似度过滤
    const KNOWLEDGE_CONFIG = getKnowledgeConfig()
    const finalThreshold = KNOWLEDGE_CONFIG.search.similarityThreshold || 0.5
    const filteredResults = searchResults.filter(result => result.similarity >= finalThreshold)

    console.log(`📊 相似度过滤: 原始结果 ${searchResults.length} 个，过滤后 ${filteredResults.length} 个 (阈值: ${(finalThreshold * 100).toFixed(0)}%)`)

    // 输出详细的搜索结果信息
    filteredResults.forEach((result, index) => {
      console.log(`  ${index + 1}. 相似度: ${(result.similarity * 100).toFixed(1)}%`)
      if (result.file_name) {
        console.log(`     来源文档: ${result.file_name}`)
      }
      console.log(`     内容预览: ${result.content.substring(0, 80)}...`)
    })

    return filteredResults
  } catch (error) {
    console.error('❌ 知识库搜索失败:', error)
    console.error('❌ 错误详情:', error.message)
    console.error('❌ 错误堆栈:', error.stack)
    return []
  }
}

/**
 * 获取知识库统计信息
 */
async function getKnowledgeStats() {
  try {
    // 只有在数据库还未初始化时才进行初始化
    if (!isKnowledgeInitialized) {
      console.log('🔧 知识库未初始化，开始初始化...')
      await initKnowledgeDatabase()
    }

    const fileCountResult = await libsqlClient.execute(
      'SELECT COUNT(*) as count FROM user_file'
    )

    const segmentCountResult = await libsqlClient.execute(
      'SELECT COUNT(*) as count FROM user_file_embd'
    )

    // 获取一些示例数据用于调试
    const sampleDataResult = await libsqlClient.execute(
      'SELECT file_content, length(file_content) as content_length FROM user_file_embd LIMIT 3'
    )

    console.log('📊 知识库统计调试信息:')
    console.log(`  - 文件数量: ${fileCountResult.rows[0].count}`)
    console.log(`  - 片段数量: ${segmentCountResult.rows[0].count}`)
    console.log('  - 示例片段:')
    sampleDataResult.rows.forEach((row, index) => {
      console.log(`    ${index + 1}. 长度: ${row.content_length}, 内容: ${row.file_content.substring(0, 100)}...`)
    })

    return {
      totalFiles: fileCountResult.rows[0].count,
      totalSegments: segmentCountResult.rows[0].count
    }
  } catch (error) {
    console.error('❌ 获取知识库统计失败:', error)
    return { totalFiles: 0, totalSegments: 0 }
  }
}

/**
 * 获取文档内容 - 优化表格处理
 */
async function getKnowledgeDocumentContent(filePath) {
  try {
    const fileExtension = path.extname(filePath).toLowerCase()

    if (fileExtension === '.docx' || fileExtension === '.doc') {
      if (!mammoth || !TurndownService) {
        throw new Error('Word文档处理库未初始化')
      }

      console.log(`📄 处理Word文档: ${path.basename(filePath)}`)

      try {
        // 优化的Word文档转换
        const htmlResult = await mammoth.convertToHtml(
          { path: filePath },
          {
            // 优化表格转换选项
            styleMap: [
              // 保留表格结构
              "p[style-name='Table Grid'] => table > tr > td:fresh",
              "p[style-name='Table Normal'] => table > tr > td:fresh",
              // 保留标题样式
              "p[style-name='Heading 1'] => h1:fresh",
              "p[style-name='Heading 2'] => h2:fresh",
              "p[style-name='Heading 3'] => h3:fresh",
              "p[style-name='标题 1'] => h1:fresh",
              "p[style-name='标题 2'] => h2:fresh",
              "p[style-name='标题 3'] => h3:fresh"
            ],
            convertImage: (image) => {
              try {
                return mammoth.docx.paragraph({
                  children: [
                    mammoth.docx.textRun({
                      text: '[图片]'
                    })
                  ]
                });
              } catch (error) {
                console.log('📄 图片转换错误:', error);
                // 返回一个简单的替代文本，避免转换失败
                return {
                  altText: '[图片]'
                };
              }
            },
            includeDefaultStyleMap: true
          }
        );

        console.log(`📄 Word转HTML完成，长度: ${htmlResult.value.length}`)
        if (htmlResult.messages.length > 0) {
          console.log('📄 转换消息:', htmlResult.messages)
        }

        try {
          // 改进的HTML到Markdown转换，支持表格
          const turndownService = new TurndownService({
            headingStyle: 'atx',
            codeBlockStyle: 'fenced',
            bulletListMarker: '-'
          });

          // 添加表格支持规则
          turndownService.addRule('tableSupport', {
            filter: function (node) {
              return node.nodeName === 'TABLE';
            },
            replacement: function (content, node) {
              try {
                const rows = Array.from(node.querySelectorAll('tr'));
                if (rows.length === 0) return content;

                let markdownTable = '\n\n';

                rows.forEach((row, rowIndex) => {
                  const cells = Array.from(row.querySelectorAll('td, th'));
                  const cellContents = cells.map(cell =>
                    cell.textContent.trim().replace(/\|/g, '\\|').replace(/\n/g, ' ')
                  );

                  // 确保至少有一列
                  if (cellContents.length === 0) {
                    cellContents.push('');
                  }

                  markdownTable += '| ' + cellContents.join(' | ') + ' |\n';

                  // 在第一行后添加分隔符
                  if (rowIndex === 0) {
                    const separator = '| ' + cellContents.map(() => '---').join(' | ') + ' |\n';
                    markdownTable += separator;
                  }
                });

                markdownTable += '\n';
                return markdownTable;
              } catch (tableError) {
                console.error('📄 表格处理错误:', tableError);
                return '\n\n[表格内容]\n\n'; // 返回简单替代内容
              }
            }
          });

          let markdownContent = turndownService.turndown(htmlResult.value);

          // 后处理：清理和优化表格格式
          markdownContent = postProcessMarkdown(markdownContent);

          console.log(`📄 转换为Markdown完成，最终长度: ${markdownContent.length}`)
          return markdownContent;
        } catch (markdownError) {
          console.error('📄 HTML转Markdown错误:', markdownError);

          // 回退方案1: 尝试直接提取文本内容
          try {
            console.log('📄 尝试回退方案1: 直接提取文本');
            const textResult = await mammoth.extractRawText({ path: filePath });
            console.log(`📄 提取纯文本成功，长度: ${textResult.value.length}`);
            return textResult.value;
          } catch (textError) {
            console.error('📄 提取纯文本失败:', textError);
            throw markdownError; // 如果回退也失败，抛出原始错误
          }
        }
      } catch (mammothError) {
        console.error('📄 Word文档转换错误:', mammothError);

        // 回退方案2: 使用最简配置尝试重新转换
        try {
          console.log('📄 尝试回退方案2: 使用最简配置');
          const simpleResult = await mammoth.extractRawText({ path: filePath });
          console.log(`📄 简单转换成功，长度: ${simpleResult.value.length}`);
          return simpleResult.value;
        } catch (simpleError) {
          console.error('📄 简单转换也失败:', simpleError);
          throw mammothError; // 如果回退也失败，抛出原始错误
        }
      }
    } else if (fileExtension === '.txt' || fileExtension === '.md') {
      const fsPromises = require('fs').promises
      return await fsPromises.readFile(filePath, 'utf8')
    } else {
      throw new Error(`不支持的文件格式: ${fileExtension}`)
    }
  } catch (error) {
    console.error(`❌ 文档内容获取失败: ${filePath}`, error)
    throw error
  }
}

/**
 * 后处理Markdown内容
 */
function postProcessMarkdown(content) {
  // 清理多余的空行
  content = content.replace(/\n{3,}/g, '\n\n');

  // 修复表格格式
  content = content.replace(/\|\s*\|\s*/g, '| ');
  content = content.replace(/\|\s*$/gm, '|');
  content = content.replace(/^\s*\|/gm, '|');

  // 确保表格前后有空行
  content = content.replace(/([^\n])\n(\|.*\|)/g, '$1\n\n$2');
  content = content.replace(/(\|.*\|)\n([^\n|])/g, '$1\n\n$2');

  // 清理空的表格行
  content = content.replace(/\|\s*\|\s*\|\s*\n/g, '');

  // 为表格添加标题（如果检测到是专家信息表等）
  content = content.replace(/(\|[^|]*姓名[^|]*\|[^|]*部门[^|]*\|.*\|[\r\n]+\|[-\s|:]+\|[\r\n]+)/i,
    '\n## 📊 专家信息表\n\n$1');

  content = content.replace(/(\|[^|]*项目[^|]*\|[^|]*任务[^|]*\|.*\|[\r\n]+\|[-\s|:]+\|[\r\n]+)/i,
    '\n## 📊 项目信息表\n\n$1');

  return content.trim();
}

/**
 * 简单的文档分割
 */
function splitKnowledgeDocument(content) {
  const KNOWLEDGE_CONFIG = getKnowledgeConfig()

  // 首先过滤掉图片内容和其他不相关内容
  const cleanContent = content
    // 移除base64图片
    .replace(/!\[.*?\]\(data:image\/[^)]+\)/g, '')
    // 移除普通图片链接
    .replace(/!\[.*?\]\([^)]+\.(png|jpg|jpeg|gif|webp)[^)]*\)/g, '')
    // 移除HTML图片标签
    .replace(/<img[^>]*>/g, '')
    // 移除多余空行
    .replace(/\n\s*\n\s*\n/g, '\n\n')
    .trim()

  console.log(`📝 文档清理前长度: ${content.length}, 清理后长度: ${cleanContent.length}`)

  const chunks = []
  const paragraphs = cleanContent.split(/\n\s*\n/)

  let currentChunk = ''
  const maxLength = KNOWLEDGE_CONFIG.document.maxSplitLength

  for (const paragraph of paragraphs) {
    // 跳过只包含图片描述或无意义内容的段落
    if (paragraph.trim().length < 10 ||
      paragraph.includes('data:image/') ||
      /^!\[.*?\]/.test(paragraph.trim())) {
      continue
    }

    if ((currentChunk + paragraph).length > maxLength && currentChunk.length > 0) {
      chunks.push(currentChunk.trim())
      currentChunk = paragraph
    } else {
      currentChunk += (currentChunk ? '\n\n' : '') + paragraph
    }
  }

  if (currentChunk.trim()) {
    chunks.push(currentChunk.trim())
  }

  // 过滤有效内容
  const filteredChunks = chunks.filter(chunk => {
    const trimmed = chunk.trim()
    return trimmed.length > 20 &&
      !trimmed.includes('data:image/') &&
      !/^!\[.*?\]/.test(trimmed)
  })

  console.log(`📝 文档分割结果: ${filteredChunks.length} 个有效片段`)

  // 如果过滤后没有片段，但原文档有内容，则保留整个清理后的文档作为一个片段
  if (filteredChunks.length === 0 && cleanContent.length > 20) {
    console.log('📝 文档内容较短，保留整个清理后的文档作为单个片段')
    return [cleanContent]
  }

  return filteredChunks
}

/**
 * 索引单个文档
 */
async function indexKnowledgeDocument(filePath) {
  try {
    if (!isKnowledgeInitialized) {
      await initKnowledgeDatabase()
    }

    console.log(`📄 开始索引文档: ${filePath}`)

    // 获取文档内容
    const fileContent = await getKnowledgeDocumentContent(filePath)

    // 分割文档
    const chunks = splitKnowledgeDocument(fileContent)

    if (chunks.length === 0) {
      throw new Error('文档分割后没有有效内容')
    }

    // 获取文件大小
    let fileSize = 0
    try {
      const fs = require('fs')
      if (fs.existsSync(filePath)) {
        const stats = fs.statSync(filePath)
        fileSize = stats.size
      }
    } catch (error) {
      console.warn(`⚠️ 无法获取文件大小: ${filePath}`, error.message)
    }

    // 插入文档记录
    const fileName = path.basename(filePath)
    const filePreview = chunks[0].substring(0, 200) + '...'

    const fileResult = await libsqlClient.execute({
      sql: `INSERT INTO user_file (file_name, file_path, source_file_path, file_preview, remark, file_size)
            VALUES (?, ?, ?, ?, ?, ?) RETURNING id`,
      args: [fileName, filePath, filePath, filePreview, `通过知识库自动索引于 ${new Date().toLocaleString()}`, fileSize]
    })

    const fileId = fileResult.rows[0].id
    console.log(`✅ 文档记录插入成功，ID: ${fileId}`)

    // 处理每个分割片段
    let successCount = 0
    for (let i = 0; i < chunks.length; i++) {
      const chunk = chunks[i]
      try {
        console.log(`📝 处理第 ${i + 1}/${chunks.length} 个片段，长度: ${chunk.length} 字符`)

        // 跳过过短的片段
        if (chunk.trim().length < 10) {
          console.log(`⏭️ 跳过过短片段 (${chunk.length} 字符)`)
          continue
        }

        const embedding = await getKnowledgeEmbedding(chunk)

        // 验证embedding格式
        if (!embedding || embedding.length === 0) {
          throw new Error('生成的embedding为空')
        }

        console.log(`💾 插入片段到数据库，embedding维度: ${embedding.length}`)

        // 转换embedding为正确的格式
        const embeddingArray = Array.from(embedding)
        console.log(`🔄 转换embedding格式，长度: ${embeddingArray.length}`)

        // 添加向量索引错误的重试机制
        let insertSuccess = false
        let retryCount = 0
        const maxRetries = 2

        while (!insertSuccess && retryCount <= maxRetries) {
          try {
            await libsqlClient.execute({
              sql: `INSERT INTO user_file_embd (file_id, file_content, embedding)
                    VALUES (?, ?, vector32(?))`,
              args: [fileId, chunk, JSON.stringify(embeddingArray)]
            })
            insertSuccess = true
            console.log(`✅ 第 ${i + 1} 个片段索引成功`)
          } catch (insertError) {
            retryCount++
            console.error(`❌ 第 ${i + 1} 个片段插入失败 (尝试 ${retryCount}/${maxRetries + 1}):`, insertError.message)

            // 检查是否是向量索引相关的错误
            if (insertError.message.includes('vector index') || insertError.message.includes('shadow row')) {
              console.log(`🔧 检测到向量索引错误，尝试修复...`)

              if (retryCount === 1) {
                // 第一次重试：尝试重建向量索引
                try {
                  console.log('🔄 重建向量索引...')
                  await libsqlClient.execute('DROP INDEX IF EXISTS file_embedding_idx')
                  await libsqlClient.execute(
                    'CREATE INDEX IF NOT EXISTS file_embedding_idx ON user_file_embd(libsql_vector_idx(embedding))'
                  )
                  console.log('✅ 向量索引重建完成，重试插入...')
                } catch (rebuildError) {
                  console.error('⚠️ 向量索引重建失败:', rebuildError.message)
                }
              } else if (retryCount === 2) {
                // 第二次重试：尝试不使用向量索引的插入
                try {
                  console.log('🔄 尝试不使用向量索引的插入...')
                  await libsqlClient.execute({
                    sql: `INSERT INTO user_file_embd (file_id, file_content, embedding)
                          VALUES (?, ?, ?)`,
                    args: [fileId, chunk, embeddingArray]
                  })
                  insertSuccess = true
                  console.log(`✅ 第 ${i + 1} 个片段索引成功（无向量索引模式）`)
                } catch (fallbackError) {
                  console.error(`❌ 无向量索引模式也失败:`, fallbackError.message)
                }
              }
            } else {
              // 非向量索引错误，直接退出重试
              break
            }

            // 添加延迟避免连续重试过快
            if (retryCount <= maxRetries && !insertSuccess) {
              await new Promise(resolve => setTimeout(resolve, 500))
            }
          }
        }

        if (insertSuccess) {
          successCount++
        } else {
          console.error(`❌ 第 ${i + 1} 个片段最终索引失败`)
        }

        // 添加短暂延迟，避免API请求过于频繁
        if (i < chunks.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 100))
        }
      } catch (error) {
        console.error(`❌ 第 ${i + 1} 个片段索引失败:`, error)
        console.error(`❌ 片段长度: ${chunk.length} 字符`)
        console.error(`❌ 片段内容预览: ${chunk.substring(0, 100)}...`)
      }
    }

    console.log(`🎉 文档索引完成: ${fileName}, 成功索引 ${successCount}/${chunks.length} 个片段`)

    return {
      success: true,
      fileId,
      fileName,
      totalSegments: chunks.length,
      successfulSegments: successCount
    }
  } catch (error) {
    console.error('❌ 文档索引失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}

/**
 * 完全清空知识库 - 清空所有表和索引
 */
async function clearKnowledgeBase() {
  try {
    if (!isKnowledgeInitialized) {
      await initKnowledgeDatabase()
    }

    console.log('🧹 开始完全清空知识库...')

    // 1. 删除向量索引
    try {
      await libsqlClient.execute('DROP INDEX IF EXISTS file_embedding_idx')
      console.log('✅ 向量索引已删除')
    } catch (error) {
      console.warn('⚠️ 删除向量索引时出错:', error.message)
    }

    // 2. 清空所有数据表
    const tables = ['user_file_embd', 'user_file']
    for (const table of tables) {
      try {
        const result = await libsqlClient.execute(`DELETE FROM ${table}`)
        console.log(`✅ 表 ${table} 已清空，删除了 ${result.rowsAffected || 0} 条记录`)
      } catch (error) {
        console.warn(`⚠️ 清空表 ${table} 时出错:`, error.message)
      }
    }

    // 3. 重置自增ID
    try {
      await libsqlClient.batch([
        'DELETE FROM sqlite_sequence WHERE name = "user_file"',
        'DELETE FROM sqlite_sequence WHERE name = "user_file_embd"'
      ], 'write')
      console.log('✅ 自增ID已重置')
    } catch (error) {
      console.warn('⚠️ 重置自增ID时出错:', error.message)
    }

    // 4. 重新创建向量索引
    try {
      await libsqlClient.execute(
        'CREATE INDEX IF NOT EXISTS file_embedding_idx ON user_file_embd(libsql_vector_idx(embedding))'
      )
      console.log('✅ 向量索引已重新创建')
    } catch (error) {
      console.warn('⚠️ 重新创建向量索引时出错:', error.message)
    }

    // 5. 执行VACUUM以回收空间
    try {
      await libsqlClient.execute('VACUUM')
      console.log('✅ 数据库空间已回收')
    } catch (error) {
      console.warn('⚠️ 数据库VACUUM时出错:', error.message)
    }

    console.log('✅ 知识库完全清空完成')
    return { success: true, message: '知识库已完全清空并重置' }
  } catch (error) {
    console.error('❌ 清空知识库失败:', error)
    return { success: false, error: error.message }
  }
}

/**
 * 完全重建知识库（删除所有数据和表结构，重新创建）
 */
async function rebuildKnowledgeBase() {
  try {
    console.log('🔄 开始完全重建知识库...')

    // 重置初始化状态
    isKnowledgeInitialized = false

    if (!libsqlClient) {
      const success = await initializeKnowledgeDependencies()
      if (!success) {
        throw new Error('依赖初始化失败')
      }
    }

    console.log('🗑️ 步骤1: 删除所有索引...')

    // 删除所有可能的索引
    const indexes = ['file_embedding_idx', 'idx_user_file_embd_file_id', 'idx_user_file_create_time']
    for (const index of indexes) {
      try {
        await libsqlClient.execute(`DROP INDEX IF EXISTS ${index}`)
        console.log(`✅ 索引 ${index} 已删除`)
      } catch (error) {
        console.warn(`⚠️ 删除索引 ${index} 时出错:`, error.message)
      }
    }

    console.log('🗑️ 步骤2: 删除所有数据表...')

    // 删除所有相关表
    const tables = ['user_file_embd', 'user_file']
    for (const table of tables) {
      try {
        await libsqlClient.execute(`DROP TABLE IF EXISTS ${table}`)
        console.log(`✅ 表 ${table} 已删除`)
      } catch (error) {
        console.warn(`⚠️ 删除表 ${table} 时出错:`, error.message)
      }
    }

    console.log('🗑️ 步骤3: 清理系统表...')

    // 清理SQLite系统表中的相关记录
    try {
      await libsqlClient.batch([
        'DELETE FROM sqlite_sequence WHERE name IN ("user_file", "user_file_embd")',
        'DELETE FROM sqlite_stat1 WHERE tbl IN ("user_file", "user_file_embd")'
      ], 'write')
      console.log('✅ 系统表已清理')
    } catch (error) {
      console.warn('⚠️ 清理系统表时出错:', error.message)
    }

    console.log('🔨 步骤4: 重新创建数据库结构...')

    // 重新创建表结构
    await libsqlClient.batch([
      `CREATE TABLE user_file (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        file_name TEXT NOT NULL,
        file_path TEXT NOT NULL,
        source_file_path TEXT NOT NULL,
        file_preview TEXT NOT NULL,
        remark TEXT NOT NULL,
        file_size INTEGER DEFAULT 0,
        create_time DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,
      `CREATE TABLE user_file_embd (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        file_id INTEGER NOT NULL,
        file_content TEXT NOT NULL,
        embedding F32_BLOB(1024),
        create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (file_id) REFERENCES user_file(id) ON DELETE CASCADE
      )`
    ], 'write')

    console.log('✅ 数据表重新创建完成')

    console.log('🔨 步骤5: 重新创建索引...')

    // 重新创建向量索引
    try {
      await libsqlClient.execute(
        'CREATE INDEX file_embedding_idx ON user_file_embd(libsql_vector_idx(embedding))'
      )
      console.log('✅ 向量索引重新创建成功')
    } catch (indexError) {
      console.warn('⚠️ 向量索引重新创建失败:', indexError.message)
      console.log('🔄 尝试不使用向量索引继续运行...')
    }

    // 创建其他有用的索引
    try {
      await libsqlClient.batch([
        'CREATE INDEX IF NOT EXISTS idx_user_file_embd_file_id ON user_file_embd(file_id)',
        'CREATE INDEX IF NOT EXISTS idx_user_file_create_time ON user_file(create_time)',
        'CREATE INDEX IF NOT EXISTS idx_user_file_embd_create_time ON user_file_embd(create_time)'
      ], 'write')
      console.log('✅ 辅助索引创建成功')
    } catch (indexError) {
      console.warn('⚠️ 辅助索引创建失败:', indexError.message)
    }

    console.log('🔨 步骤6: 优化数据库...')

    // 分析数据库统计信息
    try {
      await libsqlClient.execute('ANALYZE')
      console.log('✅ 数据库统计信息已更新')
    } catch (error) {
      console.warn('⚠️ 更新统计信息时出错:', error.message)
    }

    // 执行VACUUM以优化数据库文件
    try {
      await libsqlClient.execute('VACUUM')
      console.log('✅ 数据库已优化，空间已回收')
    } catch (error) {
      console.warn('⚠️ 数据库VACUUM时出错:', error.message)
    }

    // 设置初始化状态
    isKnowledgeInitialized = true

    console.log('🎉 知识库完全重建成功!')
    console.log('📊 重建统计:')
    console.log('  - 表: user_file, user_file_embd')
    console.log('  - 索引: file_embedding_idx + 3个辅助索引')
    console.log('  - 数据库已优化和压缩')

    return {
      success: true,
      message: '知识库已完全重建',
      tables: ['user_file', 'user_file_embd'],
      indexes: ['file_embedding_idx', 'idx_user_file_embd_file_id', 'idx_user_file_create_time', 'idx_user_file_embd_create_time']
    }
  } catch (error) {
    console.error('❌ 知识库重建失败:', error)
    isKnowledgeInitialized = false
    return { success: false, error: error.message }
  }
}

/**
 * 获取目录下的文档文件
 */
async function getKnowledgeDocumentFiles(dirPath) {
  try {
    const KNOWLEDGE_CONFIG = getKnowledgeConfig()
    const files = []
    const fsPromises = require('fs').promises
    const entries = await fsPromises.readdir(dirPath, { withFileTypes: true })

    for (const entry of entries) {
      if (entry.isFile()) {
        const ext = path.extname(entry.name).toLowerCase()
        if (KNOWLEDGE_CONFIG.document.supportedFormats.includes(ext)) {
          files.push(path.join(dirPath, entry.name))
        }
      }
    }

    return files
  } catch (error) {
    console.error('❌ 获取文件列表失败:', error)
    return []
  }
}

module.exports = {
  initializeKnowledgeDependencies,
  initKnowledgeDatabase,
  getKnowledgeEmbedding,
  searchKnowledge,
  getKnowledgeStats,
  getKnowledgeDocumentContent,
  postProcessMarkdown,
  splitKnowledgeDocument,
  indexKnowledgeDocument,
  clearKnowledgeBase,
  rebuildKnowledgeBase,
  getKnowledgeDocumentFiles,
  // 导出全局变量的getter
  getLibsqlClient: () => libsqlClient,
  getIsKnowledgeInitialized: () => isKnowledgeInitialized
}
