<template>
  <div class="email-config">
    <h3>邮件服务器配置</h3>
    <div class="form-group">
      <label>邮箱账号</label>
      <input v-model="config.user" type="email" placeholder="请输入邮箱账号">
      <span class="hint">例如：<EMAIL></span>
    </div>
    <div class="form-group">
      <label>授权码</label>
      <input v-model="config.pass" type="password" placeholder="请输入邮箱授权码">
      <span class="hint">QQ邮箱授权码获取方式：设置 -> 账户 -> POP3/IMAP/SMTP/Exchange/CardDAV/CalDAV服务</span>
    </div>
    <!-- <div class="form-group">
      <label>配置存储方式</label>
      <div class="radio-group">
        <label class="radio-item">
          <input v-model="config.storageType" type="radio" value="file">
          <span>文件存储（推荐）</span>
        </label>
        <label class="radio-item">
          <input v-model="config.storageType" type="radio" value="code">
          <span>代码存储</span>
        </label>
      </div>
      <span class="hint">文件存储：配置保存在用户数据目录；代码存储：直接修改代码文件</span>
    </div> -->
    <div class="form-group">
      <label>SMTP服务器</label>
      <input v-model="config.smtpServer" type="text" placeholder="例如: smtp.qq.com" readonly class="readonly">
      <span class="hint">QQ邮箱默认SMTP服务器：smtp.qq.com</span>
    </div>
    <div class="form-group">
      <label>SMTP端口</label>
      <input v-model.number="config.smtpPort" type="number" placeholder="例如: 465" readonly class="readonly">
      <span class="hint">QQ邮箱默认SMTP端口：465</span>
    </div>
    <div class="form-group">
      <label>SMTP SSL</label>
      <div class="checkbox-wrapper">
        <input v-model="config.smtpSsl" type="checkbox" disabled class="readonly">
        <span class="hint">（必须启用SSL）</span>
      </div>
    </div>
    <div class="form-group">
      <label>IMAP服务器</label>
      <input v-model="config.imapServer" type="text" placeholder="例如: imap.qq.com" readonly class="readonly">
      <span class="hint">QQ邮箱默认IMAP服务器：imap.qq.com</span>
    </div>
    <div class="form-group">
      <label>IMAP端口</label>
      <input v-model.number="config.imapPort" type="number" placeholder="例如: 993" readonly class="readonly">
      <span class="hint">QQ邮箱默认IMAP端口：993</span>
    </div>
    <div class="form-group">
      <label>IMAP SSL</label>
      <div class="checkbox-wrapper">
        <input v-model="config.imapSsl" type="checkbox" disabled class="readonly">
        <span class="hint">（必须启用SSL）</span>
      </div>
    </div>
    <div class="form-actions">
      <button @click="saveConfig" class="action-btn primary">保存配置</button>
      <button @click="testConfig" class="action-btn info">测试连接</button>
      <button @click="deleteConfig" class="action-btn danger">删除配置</button>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'

export default {
  name: 'EmailConfig',
  setup() {
    const config = ref({
      user: '',
      pass: '',
      smtpServer: 'smtp.qq.com',
      smtpPort: 465,
      smtpSsl: true,
      imapServer: 'imap.qq.com',
      imapPort: 993,
      imapSsl: true,
      storageType: 'file' // 默认使用文件存储
    })

    const loadConfig = async () => {
      try {
        const savedConfig = await window.electronAPI.invoke('get-email-config')
        if (savedConfig) {
          config.value = { ...config.value, ...savedConfig }
        }
      } catch (error) {
        console.error('加载邮件配置失败:', error)
      }
    }

    const saveConfig = async () => {
      if (!config.value.user || !config.value.pass) {
        alert('请填写邮箱账号和授权码')
        return
      }
      
      // 确认代码存储方式
      if (config.value.storageType === 'code') {
        const confirmed = confirm('选择代码存储方式会直接修改代码文件，确定要继续吗？\n\n注意：代码存储方式不够安全，建议使用文件存储方式。')
        if (!confirmed) {
          return
        }
      }
      
      try {
        const configToSave = {
          user: config.value.user,
          pass: config.value.pass,
          smtpServer: 'smtp.qq.com',
          smtpPort: 465,
          smtpSsl: true,
          imapServer: 'imap.qq.com',
          imapPort: 993,
          imapSsl: true,
          storageType: config.value.storageType || 'file'
        }
        
        const result = await window.electronAPI.invoke('save-email-config', configToSave)
        
        if (result.success) {
          alert('配置保存成功！邮件服务已重启以使配置生效。')
        } else {
          alert('配置保存失败: ' + (result.error || '未知错误'))
        }
      } catch (error) {
        console.error('保存配置失败:', error)
        alert('配置保存失败: ' + (error.message || '未知错误'))
      }
    }

    const testConfig = async () => {
      if (!config.value.user || !config.value.pass) {
        alert('请先填写邮箱账号和授权码')
        return
      }
      try {
        const configToTest = {
          user: config.value.user,
          pass: config.value.pass,
          smtpServer: 'smtp.qq.com',
          smtpPort: 465,
          smtpSsl: true,
          imapServer: 'imap.qq.com',
          imapPort: 993,
          imapSsl: true
        }
        await window.electronAPI.invoke('test-email-config', configToTest)
        alert('连接测试成功')
      } catch (error) {
        console.error('测试连接失败:', error)
        alert('连接测试失败: ' + (error.message || '未知错误'))
      }
    }

    const deleteConfig = async () => {
      const confirmed = confirm('确定要删除邮箱配置吗？\n\n删除后将：\n• 停止邮件MCP服务\n• 停止待办事项轮询\n• 删除所有待办事项\n• 清除所有提醒')
      
      if (!confirmed) {
        return
      }
      
      try {
        const result = await window.electronAPI.invoke('delete-email-config')
        
        if (result.success) {
          alert('邮箱配置已删除！')
          // 清空表单
          config.value = {
            user: '',
            pass: '',
            smtpServer: 'smtp.qq.com',
            smtpPort: 465,
            smtpSsl: true,
            imapServer: 'imap.qq.com',
            imapPort: 993,
            imapSsl: true,
            storageType: 'file'
          }
        } else {
          alert('删除配置失败: ' + (result.error || '未知错误'))
        }
      } catch (error) {
        console.error('删除配置失败:', error)
        alert('删除配置失败: ' + (error.message || '未知错误'))
      }
    }

    onMounted(() => {
      loadConfig()
    })

    return {
      config,
      saveConfig,
      testConfig,
      deleteConfig
    }
  }
}
</script>

<style scoped>
.email-config {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 20px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
  color: #333;
}

.form-group input[type="text"],
.form-group input[type="email"],
.form-group input[type="password"],
.form-group input[type="number"] {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.form-group input.readonly {
  background-color: #f5f5f5;
  cursor: not-allowed;
  color: #666;
}

.checkbox-wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
}

.form-group input[type="checkbox"].readonly {
  opacity: 0.7;
  cursor: not-allowed;
}

.radio-group {
  display: flex;
  gap: 20px;
  margin-top: 5px;
}

.radio-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: normal;
  cursor: pointer;
}

.radio-item input[type="radio"] {
  width: auto;
  margin: 0;
  cursor: pointer;
}

.radio-item span {
  font-size: 14px;
  color: #333;
}

.hint {
  display: block;
  margin-top: 4px;
  font-size: 12px;
  color: #666;
}

.form-actions {
  margin-top: 20px;
  display: flex;
  gap: 10px;
}

.action-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.action-btn.primary {
  background-color: #4CAF50;
  color: white;
}

.action-btn.primary:hover {
  background-color: #45a049;
}

.action-btn.info {
  background-color: #2196F3;
  color: white;
}

.action-btn.info:hover {
  background-color: #1976D2;
}

.action-btn.danger {
  background-color: #f44336;
  color: white;
}

.action-btn.danger:hover {
  background-color: #d32f2f;
}

h3 {
  margin-bottom: 20px;
  color: #333;
  text-align: center;
}
</style> 