/**
 * 知识库客户端 - 渲染进程版本
 * 通过IPC与主进程的知识库服务通信
 * 参考demo/libsql实现，提供完整的知识库操作功能
 */
import { KNOWLEDGE_CONFIG } from './config.js'

/**
 * 检查Electron API是否可用
 */
function checkElectronAPI() {
  if (!window.electronAPI) {
    throw new Error('Electron API不可用，请确保在Electron环境中运行')
  }
  return true
}

/**
 * 初始化知识库数据库
 */
export async function initKnowledgeDatabase() {
  try {
    checkElectronAPI()
    console.log('🧠 通过IPC初始化知识库数据库')
    
    const result = await window.electronAPI.invoke('knowledge-init')
    console.log('🧠 知识库初始化结果:', result)
    return result
  } catch (error) {
    console.error('❌ 知识库数据库初始化失败:', error)
    return { success: false, error: error.message }
  }
}

/**
 * 索引单个文档
 */
export async function indexDocument(filePath, fileType = 1, remark = '文档') {
  try {
    checkElectronAPI()
    console.log(`🧠 通过IPC索引文档: ${filePath}`)
    
    const result = await window.electronAPI.invoke('knowledge-index-document', filePath, fileType, remark)
    console.log('🧠 文档索引结果:', result)
    return result
  } catch (error) {
    console.error('❌ 文档索引失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}

/**
 * 批量索引文档
 */
export async function indexDocuments(filePaths, fileType = 1, onProgress = null) {
  try {
    checkElectronAPI()
    console.log(`🧠 批量索引 ${filePaths.length} 个文档`)
    
    const results = []
    let successCount = 0
    
    for (let i = 0; i < filePaths.length; i++) {
      const filePath = filePaths[i]
      
      if (onProgress) {
        onProgress(i + 1, filePaths.length, filePath)
      }
      
      try {
        const result = await indexDocument(filePath, fileType, `批量导入 ${new Date().toLocaleString()}`)
        results.push({ filePath, result })
        
        if (result.success) {
          successCount++
        }
        
        // 添加延迟，避免过于频繁的API调用
        if (i < filePaths.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 500))
        }
      } catch (error) {
        console.error(`❌ 索引文档失败: ${filePath}`, error)
        results.push({ 
          filePath, 
          result: { success: false, error: error.message } 
        })
      }
    }
    
    console.log(`🧠 批量索引完成: ${successCount}/${filePaths.length} 成功`)
    
    return {
      success: true,
      totalFiles: filePaths.length,
      successCount,
      results
    }
  } catch (error) {
    console.error('❌ 批量索引失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}

/**
 * 搜索知识库
 */
export async function searchKnowledge(query, limit = null, fileType = null) {
  try {
    checkElectronAPI()
    const searchLimit = limit || KNOWLEDGE_CONFIG.search.defaultLimit
    
    console.log(`🧠 通过IPC搜索知识库: "${query}"`)
    
    const searchResults = await window.electronAPI.invoke('knowledge-search', query, searchLimit, fileType)
    
    // 应用相似度过滤
    const { getKnowledgeConfig } = await import('../config/modelConfig.js')
    const knowledgeConfig = getKnowledgeConfig()
    const similarityThreshold = knowledgeConfig.search.similarityThreshold || 0.5
    
    const filteredResults = searchResults.filter(result => result.similarity >= similarityThreshold)
    
    console.log(`✅ 找到 ${searchResults.length} 个候选片段，过滤后 ${filteredResults.length} 个高质量片段 (阈值: ${(similarityThreshold * 100).toFixed(0)}%)`)
    
    // 统计涉及的文档数量
    const documentSources = new Set(filteredResults.map(r => r.file_name).filter(Boolean))
    console.log(`📚 涉及文档: ${documentSources.size} 个 - ${Array.from(documentSources).join(', ')}`)
    
    filteredResults.forEach((result, index) => {
      console.log(`  ${index + 1}. 相似度: ${(result.similarity * 100).toFixed(1)}%`)
      if (result.file_name) {
        console.log(`     来源文档: ${result.file_name}`)
      }
      console.log(`     内容预览: ${result.content}...`)
    })
    
    return filteredResults
  } catch (error) {
    console.error('❌ 知识库搜索失败:', error)
    return []
  }
}

/**
 * 智能问答
 * 基于知识库内容进行问答
 */
export async function askKnowledge(question, options = {}) {
  try {
    const {
      searchLimit = 3,
      minSimilarity = 0,
      includeContext = true
    } = options
    
    console.log(`💬 智能问答: "${question}"`)
    
    // 搜索相关知识
    const searchResults = await searchKnowledge(question, searchLimit)
    
    if (searchResults.length === 0) {
      return {
        success: true,
        answer: '抱歉，在知识库中没有找到与您问题相关的信息。',
        sources: [],
        hasKnowledge: false
      }
    }
    
    // 获取知识库配置中的相似度阈值
    const { getKnowledgeConfig } = await import('../config/modelConfig.js')
    const knowledgeConfig = getKnowledgeConfig()
    const configThreshold = knowledgeConfig.search.similarityThreshold || 0.5
    
    // 使用配置中的阈值和用户指定的最小阈值中的较大值
    const effectiveThreshold = Math.max(minSimilarity, configThreshold)
    
    // 过滤低相似度结果
    const relevantResults = searchResults.filter(r => r.similarity >= effectiveThreshold)
    
    console.log(`🔍 智能问答过滤: 原始结果 ${searchResults.length} 个，有效阈值 ${(effectiveThreshold * 100).toFixed(0)}%，过滤后 ${relevantResults.length} 个`)
    
    if (relevantResults.length === 0) {
      return {
        success: true,
        answer: '抱歉，找到的信息与您的问题相关性较低，无法提供准确答案。',
        sources: searchResults.map(r => ({
          file_name: r.file_name,
          similarity: r.similarity,
          content_preview: r.content.substring(0, 100) + '...'
        })),
        hasKnowledge: false
      }
    }
    
    // 构建知识上下文
    let knowledgeContext = '以下是相关的知识内容：\n\n'
    relevantResults.forEach((result, index) => {
      knowledgeContext += `[文档${index + 1}] ${result.file_name || '未知文档'} (相似度: ${(result.similarity * 100).toFixed(1)}%)\n`
      knowledgeContext += `${result.content}\n\n`
    })
    
    // 构建回答（这里可以集成更复杂的AI回答逻辑）
    const answer = `基于知识库内容，我为您找到了以下相关信息：

${relevantResults.map((result, index) => `
**来源 ${index + 1}**: ${result.file_name || '未知文档'} (相似度: ${(result.similarity * 100).toFixed(1)}%)

${result.content}
`).join('\n---\n')}

请注意，以上信息来自您的知识库，建议结合具体情况进行判断。`
    
    return {
      success: true,
      answer,
      sources: relevantResults.map(r => ({
        file_name: r.file_name,
        similarity: r.similarity,
        content: r.content,
        source_file_path: r.source_file_path
      })),
      hasKnowledge: true,
      knowledgeContext: includeContext ? knowledgeContext : null
    }
    
  } catch (error) {
    console.error('❌ 智能问答失败:', error)
    return {
      success: false,
      error: error.message,
      answer: '抱歉，处理您的问题时发生错误，请稍后重试。',
      sources: [],
      hasKnowledge: false
    }
  }
}

/**
 * 获取知识库统计信息
 */
export async function getKnowledgeStats() {
  try {
    checkElectronAPI()
    console.log('🧠 通过IPC获取知识库统计')
    
    const stats = await window.electronAPI.invoke('knowledge-stats')
    console.log('🧠 知识库统计结果:', stats)
    return stats
  } catch (error) {
    console.error('❌ 获取知识库统计失败:', error)
    return { totalFiles: 0, totalSegments: 0 }
  }
}

/**
 * 清空知识库
 */
export async function clearKnowledgeBase() {
  try {
    checkElectronAPI()
    console.log('🧠 通过IPC清空知识库')
    
    const result = await window.electronAPI.invoke('knowledge-clear')
    console.log('🧠 清空知识库结果:', result)
    return result
  } catch (error) {
    console.error('❌ 清空知识库失败:', error)
    return { success: false, error: error.message }
  }
}

/**
 * 重建知识库（完全重新初始化）
 */
export async function rebuildKnowledgeBase() {
  try {
    checkElectronAPI()
    console.log('🧠 通过IPC重建知识库')
    
    const result = await window.electronAPI.invoke('knowledge-rebuild')
    console.log('🧠 重建知识库结果:', result)
    return result
  } catch (error) {
    console.error('❌ 重建知识库失败:', error)
    return { success: false, error: error.message }
  }
}

/**
 * 选择文档目录
 */
export async function selectDocumentDirectory() {
  try {
    checkElectronAPI()
    console.log('🧠 选择文档目录')
    
    const result = await window.electronAPI.invoke('select-directory')
    
    if (result.canceled) {
      return { success: false, canceled: true }
    }
    
    return { 
      success: true, 
      directory: result.filePaths[0]
    }
  } catch (error) {
    console.error('❌ 选择目录失败:', error)
    return { success: false, error: error.message }
  }
}

/**
 * 获取目录中的文档文件
 */
export async function getDirectoryFiles(directory) {
  try {
    checkElectronAPI()
    console.log(`🧠 获取目录文件: ${directory}`)
    
    const files = await window.electronAPI.invoke('get-directory-files', directory)
    console.log(`🧠 找到 ${files.length} 个文档文件`)
    
    return files
  } catch (error) {
    console.error('❌ 获取目录文件失败:', error)
    return []
  }
}

/**
 * 删除文件及其相关片段
 */
export async function deleteFile(fileId) {
  try {
    checkElectronAPI()
    console.log(`🧠 删除文件: ${fileId}`)
    
    const result = await window.electronAPI.invoke('knowledge-delete-file', fileId)
    console.log('🧠 删除文件结果:', result)
    return result
  } catch (error) {
    console.error('❌ 删除文件失败:', error)
    return { success: false, error: error.message }
  }
}

/**
 * 列出知识库中的文件
 */
export async function listFiles(options = {}) {
  try {
    checkElectronAPI()
    const {
      fileType = null,
      fileName = null,
      pageSize = 10,
      pageNum = 1
    } = options
    
    console.log('🧠 列出知识库文件', options)
    
    const result = await window.electronAPI.invoke('knowledge-list-files', fileType, fileName, pageSize, pageNum)
    console.log('🧠 文件列表结果:', result)
    return result
  } catch (error) {
    console.error('❌ 列出文件失败:', error)
    return { 
      total: 0, 
      rows: [], 
      pagination: { pageSize: 10, pageNum: 1, totalPages: 0 } 
    }
  }
}

/**
 * 调试知识库状态
 */
export async function debugKnowledge() {
  try {
    checkElectronAPI()
    console.log('🧠 调试知识库状态')
    
    const result = await window.electronAPI.invoke('knowledge-debug')
    console.log('🧠 调试结果:', result)
    return result
  } catch (error) {
    console.error('❌ 调试失败:', error)
    return { error: error.message }
  }
}

/**
 * 修复知识库数据库
 */
export async function fixKnowledgeDatabase() {
  try {
    checkElectronAPI()
    console.log('🔧 开始修复知识库数据库...')
    
    const result = await window.electronAPI.invoke('fix-knowledge-database')
    console.log('🔧 知识库数据库修复结果:', result)
    return result
  } catch (error) {
    console.error('❌ 知识库数据库修复失败:', error)
    return { success: false, error: error.message }
  }
}