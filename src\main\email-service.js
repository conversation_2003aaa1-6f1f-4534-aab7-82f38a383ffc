const { ipcMain, Notification } = require('electron')
const Store = require('electron-store')
const path = require('path')
const { callMainAIService } = require('./api-client')

// === 邮件服务管理器 ===

class EmailService {
  constructor(mcpManager, mainWindow) {
    this.mcpManager = mcpManager
    this.mainWindow = mainWindow
    this.isInitialized = false
    this.pollingInterval = null
    this.todoList = []
    this.reminders = []

    // 添加持久化存储
    this.todoStore = new Store({
      name: 'todos',
      defaults: {
        todoList: [],
        lastSyncTime: null
      }
    })

    // 初始化时加载持久化的待办事项
    this.loadTodosFromStore()

    // 不在构造函数中设置IPC处理程序，避免在AppManager初始化前注册
    // 改为在initialize方法中设置
  }

  // 从持久化存储加载待办事项
  loadTodosFromStore() {
    try {
      const storedTodos = this.todoStore.get('todoList', [])
      this.todoList = storedTodos
      console.log(`📧 [EMAIL_SERVICE] 从存储加载了 ${this.todoList.length} 个待办事项`)

      // 重新设置提醒（针对未来的待办事项）
      this.restoreReminders()
    } catch (error) {
      console.error('📧 [EMAIL_SERVICE] 加载待办事项失败:', error)
      this.todoList = []
    }
  }

  // 保存待办事项到持久化存储
  saveTodosToStore() {
    try {
      this.todoStore.set('todoList', this.todoList)
      this.todoStore.set('lastSyncTime', new Date().toISOString())
      console.log(`📧 [EMAIL_SERVICE] 已保存 ${this.todoList.length} 个待办事项到存储`)
    } catch (error) {
      console.error('📧 [EMAIL_SERVICE] 保存待办事项失败:', error)
    }
  }

  // 恢复提醒设置
  restoreReminders() {
    console.log('📧 [EMAIL_SERVICE] 恢复提醒设置...')

    // 清除现有的提醒
    for (const reminder of this.reminders) {
      clearTimeout(reminder.timerId)
    }
    this.reminders = []

    // 为未完成的待办事项重新设置提醒
    const now = new Date()
    for (const todo of this.todoList) {
      if (!todo.completed && todo.dueDate) {
        try {
          const dueTime = new Date(todo.dueDate)
          const reminderTime = new Date(dueTime.getTime() - 30 * 60 * 1000) // 提前30分钟

          if (reminderTime > now) {
            const delay = reminderTime.getTime() - now.getTime()

            const reminderId = setTimeout(() => {
              this.showReminder(todo)
            }, delay)

            this.reminders.push({
              id: todo.id,
              emailId: todo.emailId,
              timerId: reminderId,
              reminderTime: reminderTime.toISOString(),
              todo: todo
            })

            console.log(`📧 [EMAIL_SERVICE] 已恢复提醒: ${todo.subject} 将在 ${reminderTime.toLocaleString()} 提醒`)
          }
        } catch (error) {
          console.error('📧 [EMAIL_SERVICE] 恢复提醒失败:', error)
        }
      }
    }

    console.log(`📧 [EMAIL_SERVICE] 恢复了 ${this.reminders.length} 个提醒`)
  }

  // 邮件配置相关IPC处理程序 - 已移至AppManager中统一管理
  setupEmailConfigHandlers() {
    console.log('📧 邮件配置处理程序已在AppManager中统一管理，跳过重复设置')
  }

  async initialize() {
    if (this.isInitialized) {
      console.log('📧 [EMAIL_SERVICE] 邮件服务已经初始化，跳过重复初始化')
      return
    }

    console.log('📧 [EMAIL_SERVICE] 开始初始化邮件服务...')

    try {
      // 设置待办事项处理程序
      console.log('📧 [EMAIL_SERVICE] 设置待办事项处理程序...')
      this.setupTodoHandlers()
      
      // 设置邮件配置处理程序（无论是否有配置都要设置，这样用户才能配置邮箱）
      console.log('📧 [EMAIL_SERVICE] 设置邮件配置处理程序...')
      this.setupEmailConfigHandlers()

      // 检查是否有邮件配置
      const store = new Store()
      const emailConfig = store.get('emailConfig')
      
      if (!emailConfig || !emailConfig.user || !emailConfig.pass) {
        console.log('⚠️ [EMAIL_SERVICE] 未找到邮件配置，邮件服务将以有限功能运行')
        this.isInitialized = true
        return
      }

      // 等待MCP服务初始化完成
      const mcpReady = await this.waitForMCPReady()
      
      if (mcpReady) {
        // 只启动轮询，不立即检查邮件
        // 首次检查将在所有服务都初始化完成后触发
        this.startPolling()
        console.log('📧 [EMAIL_SERVICE] 邮件服务初始化完成（等待所有MCP服务就绪后开始首次检查）')
      } else {
        console.log('⚠️ [EMAIL_SERVICE] 邮件MCP服务未就绪，邮件服务将以有限功能运行')
      }

      this.isInitialized = true
      console.log('✅ [EMAIL_SERVICE] 邮件服务初始化完成')
    } catch (error) {
      console.error('❌ [EMAIL_SERVICE] 邮件服务初始化失败:', error)
      this.isInitialized = true // 即使失败也要标记为已初始化，避免重复尝试
    }
  }

  // 设置待办事项处理程序
  setupTodoHandlers() {
    console.log('📋 设置待办事项处理程序...')

    // 获取待办列表
    ipcMain.handle('get-todo-list', async () => {
      try {
        console.log('📋 收到获取待办列表请求')
        return {
          success: true,
          todos: this.todoList
        }
      } catch (error) {
        console.error('❌ 获取待办列表失败:', error)
        return { success: false, error: error.message }
      }
    })

    // 手动检查邮件
    ipcMain.handle('check-emails-manual', async () => {
      try {
        console.log('📧 [EMAIL_SERVICE] 收到手动检查邮件请求')
        const result = await this.checkEmails(false)
        console.log('📧 [EMAIL_SERVICE] 手动检查邮件完成:', result)
        return result
      } catch (error) {
        console.error('❌ 手动检查邮件失败:', error)
        return { success: false, error: error.message }
      }
    })

    // 添加待办事项
    ipcMain.handle('add-todo', async (event, todo) => {
      try {
        console.log('📋 收到添加待办事项请求:', todo)

        // 生成唯一ID
        const id = Date.now().toString()
        const newTodo = {
          id,
          ...todo,
          createdAt: new Date().toISOString(),
          completed: false
        }

        // 添加到待办列表
        this.todoList.push(newTodo)

        // 保存到存储
        this.saveTodosToStore()

        console.log('✅ 待办事项已添加:', newTodo)
        return { success: true, todo: newTodo }
      } catch (error) {
        console.error('❌ 添加待办事项失败:', error)
        return { success: false, error: error.message }
      }
    })

    // 更新待办事项
    ipcMain.handle('update-todo', async (event, todo) => {
      try {
        console.log('📋 收到更新待办事项请求:', todo)

        // 查找待办事项
        const index = this.todoList.findIndex(item => item.id === todo.id)
        if (index === -1) {
          throw new Error(`未找到ID为${todo.id}的待办事项`)
        }

        // 更新待办事项
        this.todoList[index] = {
          ...this.todoList[index],
          ...todo,
          updatedAt: new Date().toISOString()
        }

        // 保存到存储
        this.saveTodosToStore()

        console.log('✅ 待办事项已更新:', this.todoList[index])
        return { success: true, todo: this.todoList[index] }
      } catch (error) {
        console.error('❌ 更新待办事项失败:', error)
        return { success: false, error: error.message }
      }
    })

    // 删除待办事项
    ipcMain.handle('delete-todo', async (event, todoId) => {
      try {
        console.log('📋 收到删除待办事项请求:', todoId)

        // 查找待办事项
        const index = this.todoList.findIndex(item => item.id === todoId)
        if (index === -1) {
          throw new Error(`未找到ID为${todoId}的待办事项`)
        }

        // 删除待办事项
        const deletedTodo = this.todoList.splice(index, 1)[0]

        // 保存到存储
        this.saveTodosToStore()

        console.log('✅ 待办事项已删除:', deletedTodo)
        return { success: true, todoId }
      } catch (error) {
        console.error('❌ 删除待办事项失败:', error)
        return { success: false, error: error.message }
      }
    })

    // 清除提醒
    ipcMain.handle('clear-reminder', async (event, reminderId) => {
      try {
        console.log('🔔 收到清除提醒请求:', reminderId)

        // 查找对应的待办事项
        const todo = this.todoList.find(item => item.id === reminderId)
        if (todo) {
          // 更新提醒状态
          todo.reminderShown = true
          todo.reminderDismissed = true

          // 保存到存储
          this.saveTodosToStore()

          console.log('✅ 提醒已清除:', reminderId)
          return { success: true }
        } else {
          console.warn('⚠️ 未找到对应的提醒:', reminderId)
          return { success: false, error: '未找到对应的提醒' }
        }
      } catch (error) {
        console.error('❌ 清除提醒失败:', error)
        return { success: false, error: error.message }
      }
    })

    console.log('✅ 待办事项处理程序设置完成')
  }

  /**
   * 开始首次邮件检查（在所有MCP服务初始化完成后调用）
   */
  async startInitialEmailCheck() {
    if (!this.isInitialized) {
      console.warn('📧 [EMAIL_SERVICE] 邮件服务未初始化，跳过首次检查')
      return
    }

    console.log('📧 [EMAIL_SERVICE] 开始首次邮件检查（查询近24小时）')
    await this.checkEmails(true)
  }

  async waitForMCPReady() {
    console.log('📧 [EMAIL_SERVICE] 等待邮件MCP服务启动...')

    let attempts = 0
    const maxAttempts = 30 // 最多等待30秒

    while (attempts < maxAttempts) {
      const emailClient = this.mcpManager.clients.get('email-server')
      if (emailClient && emailClient.isConnected && emailClient.mcpClient) {
        console.log('📧 [EMAIL_SERVICE] 邮件MCP服务已就绪')
        return true
      }

      console.log(`📧 [EMAIL_SERVICE] 等待中... (${attempts + 1}/${maxAttempts})`)
      await new Promise(resolve => setTimeout(resolve, 1000))
      attempts++
    }

    console.warn('📧 [EMAIL_SERVICE] 邮件MCP服务启动超时，将使用模拟模式')
    return false
  }

  async checkEmails(isInitial = false) {
    try {
      // 检查是否有邮件配置
      const store = new Store()
      const emailConfig = store.get('emailConfig')

      if (!emailConfig || !emailConfig.user || !emailConfig.pass) {
        console.log('⚠️ [EMAIL_SERVICE] 未配置邮箱，跳过邮件检查')
        return { success: true, message: '未配置邮箱，跳过邮件检查', emails: [], todos: [] }
      }

      console.log('📧 [EMAIL_SERVICE] 开始检查未读邮件...')

      // 计算时间范围
      const now = new Date()
      let startTime

      if (isInitial) {
        // 初始化时查询近24小时的邮件
        startTime = new Date(now.getTime() - 24 * 60 * 60 * 1000)
        console.log('📧 [EMAIL_SERVICE] 初始化模式：查询近24小时的未读邮件')
      } else {
        // 定期轮询时查询近30分钟的邮件
        startTime = new Date(now.getTime() - 30 * 60 * 1000)
        console.log('📧 [EMAIL_SERVICE] 轮询模式：查询近30分钟的未读邮件')
      }

      // 使用本地时间而不是UTC时间
      const formatLocalTime = (date) => {
        const year = date.getFullYear()
        const month = String(date.getMonth() + 1).padStart(2, '0')
        const day = String(date.getDate()).padStart(2, '0')
        const hours = String(date.getHours()).padStart(2, '0')
        const minutes = String(date.getMinutes()).padStart(2, '0')
        const seconds = String(date.getSeconds()).padStart(2, '0')
        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
      }

      const startTimeStr = formatLocalTime(startTime)
      const endTimeStr = formatLocalTime(now)

      // 显示时区信息用于调试
      const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone
      const timezoneOffset = now.getTimezoneOffset() / -60 // 转换为小时，负号是因为getTimezoneOffset返回的是反向的
      console.log(`📧 [EMAIL_SERVICE] 当前时区: ${timezone} (UTC${timezoneOffset >= 0 ? '+' : ''}${timezoneOffset})`)

      console.log(`📧 [EMAIL_SERVICE] 查询时间范围：${startTimeStr} 至 ${endTimeStr}`)

      // 查询指定时间范围的未读邮件
      const emailResult = await this.mcpManager.callRealMCPTool('list_email', {
        start_time: startTimeStr,
        end_time: endTimeStr
      })

      if (!emailResult.success) {
        console.error('📧 [EMAIL_SERVICE] 查询邮件失败:', emailResult.error)
        return { success: false, error: emailResult.error }
      }

      const emails = emailResult.emails || []
      console.log(`📧 [EMAIL_SERVICE] 查询到 ${emails.length} 封未读邮件`)

      if (emails.length === 0) {
        return { success: true, message: '没有新邮件', emails: [], todos: [] }
      }

      // 标记邮件为已读
      const uids = emails.map(email => email.uid).filter(uid => uid)
      if (uids.length > 0) {
        const markResult = await this.mcpManager.callRealMCPTool('mark_email_as_read', { uid_list: uids })
        console.log('📧 [EMAIL_SERVICE] 邮件标记结果:', markResult.success ? '成功' : '失败')
      }

      // 筛选待办类邮件
      const todoEmails = await this.extractTodoEmails(emails)
      console.log(`📧 [EMAIL_SERVICE] 筛选出 ${todoEmails.length} 封待办邮件`)

      // 更新待办列表
      this.updateTodoList(todoEmails)

      // 设置提醒
      this.scheduleReminders(todoEmails)

      // 发送通知到渲染进程
      if (this.mainWindow && this.mainWindow.webContents) {
        this.mainWindow.webContents.send('new-emails-processed', {
          totalEmails: emails.length,
          todoEmails: todoEmails.length,
          todos: this.todoList
        })
      }

      return {
        success: true,
        message: `处理了 ${emails.length} 封邮件，其中 ${todoEmails.length} 封为待办类邮件`,
        emails,
        todos: todoEmails
      }

    } catch (error) {
      console.error('📧 [EMAIL_SERVICE] 检查邮件时出错:', error)
      return { success: false, error: error.message }
    }
  }

  async extractTodoEmails(emails) {
    try {
      console.log('📧 [EMAIL_SERVICE] 开始AI筛选待办邮件...')

      // 构建邮件摘要
      const emailSummaries = emails.map((email, index) => ({
        id: index + 1,
        subject: email.subject || '无主题',
        from: email.from || '未知发件人',
        preview: (email.body || '').substring(0, 200) + '...',
        date: email.date || '未知时间'
      }))

      // 构建AI请求
      const prompt = `请分析以下邮件，筛选出需要用户执行具体行动的待办类邮件。待办类邮件包括：
1. 会议邀请和安排
2. 任务分配和工作要求
3. 截止日期提醒
4. 需要回复或处理的重要事项
5. 审批、确认类请求

请只返回JSON格式的结果，包含待办邮件的ID和提取的待办事项：

邮件列表：
${emailSummaries.map(email => `${email.id}. 主题：${email.subject}\n发件人：${email.from}\n内容预览：${email.preview}\n时间：${email.date}\n`).join('\n')}

请返回格式：
{
  "todoEmails": [
    {
      "emailId": 1,
      "todoType": "meeting|task|deadline|reply|approval",
      "todoDescription": "具体的待办事项描述",
      "urgency": "high|medium|low",
      "dueDate": "如果能从邮件中提取到截止时间，格式YYYY-MM-DD HH:MM"
    }
  ]}`

      // 调用AI接口
      const response = await this.callAIService(prompt)

      if (!response.success) {
        console.error('📧 [EMAIL_SERVICE] AI筛选失败:', response.error)
        return []
      }

      const aiResult = response.data
      const todoEmails = []

      for (const todoItem of aiResult.todoEmails || []) {
        const originalEmail = emails[todoItem.emailId - 1]
        if (originalEmail) {
          todoEmails.push({
            ...originalEmail,
            todoType: todoItem.todoType,
            todoDescription: todoItem.todoDescription,
            urgency: todoItem.urgency,
            dueDate: todoItem.dueDate,
            id: Date.now() + Math.random() // 生成唯一ID
          })
        }
      }

      return todoEmails

    } catch (error) {
      console.error('📧 [EMAIL_SERVICE] AI筛选邮件时出错:', error)
      return []
    }
  }

  async callAIService(prompt) {
    const requestId = Date.now()
    const startTime = Date.now()

    try {
      // 获取当前模型配置
      let config = {
        model: 'qwen-max-latest',
        maxTokens: 1000,
        temperature: 0.7,
        timeout: 30000
      }

      // 尝试从渲染进程获取当前模型配置
      if (this.mainWindow && this.mainWindow.webContents) {
        try {
          const modelConfig = await this.mainWindow.webContents.executeJavaScript(`
            (() => {
              try {
                const modelManager = require('./src/renderer/utils/modelManager.js').default;
                return modelManager.getModelConfig();
              } catch (e) {
                console.error('获取模型配置失败:', e);
                return null;
              }
            })()
          `);

          if (modelConfig) {
            config.model = modelConfig.id;
            config.isSystemDefault = modelConfig.isSystemDefault;
          }
        } catch (error) {
          console.log('无法从渲染进程获取模型配置，使用默认配置:', error.message);
        }
      }

      // 构建请求数据
      const requestData = {
        messages: [
          {
            role: 'system',
            content: `你是一个专业的邮件分析助手，专门负责从邮件中识别和提取待办事项。你的任务是：

1. 仔细分析邮件内容，识别需要用户执行具体行动的待办类邮件
2. 提取关键信息如任务类型、紧急程度、截止时间等
3. 严格按照JSON格式返回结果，不要包含任何额外文本
4. 对于不确定的信息，宁可留空也不要猜测

待办事项类型包括：
- meeting：会议邀请和安排
- task：任务分配和工作要求
- deadline：截止日期提醒
- reply：需要回复的重要事项
- approval：审批、确认类请求

紧急程度判断：
- high：明确提到紧急、立即、今天内完成
  - medium：有明确时间要求但不是特别紧急
  - low：没有明确时间要求或时间较宽松

请严格按照JSON格式返回结果，不要包含任何解释性文字。`
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: config.maxTokens,
        temperature: config.temperature
      }

      // 根据模型类型设置不同的参数
      if (config.isSystemDefault) {
        // 系统默认模型：model传空字符串
        requestData.model = ''
      } else {
        // 其他模型：传具体的model名称
        requestData.model = config.model
      }

      // 获取用户token
      let userToken = ''
      if (this.mainWindow && this.mainWindow.webContents) {
        try {
          userToken = await this.mainWindow.webContents.executeJavaScript(`
            localStorage.getItem('userAuthToken') || ''
          `);
        } catch (error) {
          console.log('无法获取用户token:', error.message);
        }
      }

      // 检测是否为邮件待办筛选请求
      const isEmailTodoRequest = requestData.messages &&
        requestData.messages.some(msg =>
          msg.content && msg.content.includes('邮件分析助手') &&
          msg.content.includes('待办事项')
        )

      // 详细的请求日志
      console.log('🤖 [AI_REQUEST] 开始AI请求')
      console.log('🤖 [AI_REQUEST] 请求ID:', requestId)
      console.log('🤖 [AI_REQUEST] 请求URL:', isEmailTodoRequest ? '/api/tool/email/handle' : '/chat/completions')
      console.log('🤖 [AI_REQUEST] 请求方法: POST')
      console.log('🤖 [AI_REQUEST] 请求体:', JSON.stringify(requestData, null, 2))

      // 发送请求信息到渲染进程（如果需要调试）
      if (this.mainWindow && this.mainWindow.webContents) {
        this.mainWindow.webContents.send('ai-request-start', {
          requestId,
          url: isEmailTodoRequest ? '/api/tool/email/handle' : '/chat/completions',
          method: 'POST',
          body: requestData,
          timestamp: new Date().toISOString(),
          isEmailTodo: isEmailTodoRequest
        })
      }

      // 使用统一API管理器
      const apiResult = await callMainAIService(userToken, requestData, config.isSystemDefault ? '' : config.model, isEmailTodoRequest)

      if (!apiResult.success) {
        throw new Error(apiResult.error || 'AI服务请求失败')
      }

      const duration = Date.now() - startTime
      // 处理嵌套的响应结构：response.data.data
      const responseData = apiResult.data
      const content = responseData.choices[0].message.content

      // 详细的响应日志
      console.log('🤖 [AI_RESPONSE] AI响应成功')
      console.log('🤖 [AI_RESPONSE] 请求ID:', requestId)
      console.log('🤖 [AI_RESPONSE] 响应耗时:', duration + 'ms')
      console.log('🤖 [AI_RESPONSE] 响应状态:', apiResult.status)
      console.log('🤖 [AI_RESPONSE] 响应内容:', content)

      // 发送响应信息到渲染进程（如果需要调试）
      if (this.mainWindow && this.mainWindow.webContents) {
        this.mainWindow.webContents.send('ai-request-complete', {
          requestId,
          status: apiResult.status,
          data: apiResult.data,
          duration,
          timestamp: new Date().toISOString()
        })
      }

      // 尝试解析JSON，如果失败则尝试提取JSON部分
      let parsedResult
      try {
        parsedResult = JSON.parse(content)
      } catch (parseError) {
        console.log('📧 [EMAIL_SERVICE] 直接解析JSON失败，尝试提取JSON部分')
        // 尝试从响应中提取JSON部分
        const jsonMatch = content.match(/\{[\s\S]*\}/)
        if (jsonMatch) {
          parsedResult = JSON.parse(jsonMatch[0])
        } else {
          throw new Error('无法从AI响应中解析JSON')
        }
      }

      return { success: true, data: parsedResult }

    } catch (error) {
      const duration = Date.now() - startTime

      // 详细的错误日志
      console.error('🤖 [AI_ERROR] AI请求失败')
      console.error('🤖 [AI_ERROR] 请求ID:', requestId)
      console.error('🤖 [AI_ERROR] 错误耗时:', duration + 'ms')
      console.error('🤖 [AI_ERROR] 错误类型:', error.name)
      console.error('🤖 [AI_ERROR] 错误消息:', error.message)
      console.error('🤖 [AI_ERROR] 错误堆栈:', error.stack)

      if (error.response) {
        console.error('🤖 [AI_ERROR] 响应状态:', error.response.status)
        console.error('🤖 [AI_ERROR] 响应头:', error.response.headers)
        console.error('🤖 [AI_ERROR] 响应数据:', error.response.data)
      }

      // 发送错误信息到渲染进程（如果需要调试）
      if (this.mainWindow && this.mainWindow.webContents) {
        this.mainWindow.webContents.send('ai-request-error', {
          requestId,
          error: {
            name: error.name,
            message: error.message,
            status: error.response?.status,
            data: error.response?.data
          },
          duration,
          timestamp: new Date().toISOString()
        })
      }

      return { success: false, error: error.message }
    }
  }

  updateTodoList(todoEmails) {
    // 添加新的待办事项
    const newTodos = []
    for (const email of todoEmails) {
      const existingTodo = this.todoList.find(todo => todo.emailId === email.uid)
      if (!existingTodo) {
        const newTodo = {
          id: email.id,
          emailId: email.uid,
          subject: email.subject,
          from: email.from,
          todoDescription: email.todoDescription,
          todoType: email.todoType,
          urgency: email.urgency,
          dueDate: email.dueDate,
          createdAt: new Date().toISOString(),
          completed: false
        }
        this.todoList.push(newTodo)
        newTodos.push(newTodo)
      }
    }

    console.log(`📧 [EMAIL_SERVICE] 待办列表已更新，当前共 ${this.todoList.length} 项`)

    // 自动同步新增的待办事项到Outlook日历
    if (newTodos.length > 0) {
      console.log(`📧 [EMAIL_SERVICE] 发现 ${newTodos.length} 个新待办事项，准备同步到Outlook日历`)
      this.syncNewTodosToCalendar(newTodos)
    }

    // 保存待办事项到持久化存储
    this.saveTodosToStore()
  }

  /**
   * 同步新增的待办事项到Outlook日历
   */
  async syncNewTodosToCalendar(newTodos) {
    try {
      console.log(`📅 [EMAIL_SERVICE] 开始同步 ${newTodos.length} 个待办事项到Outlook日历`)

      // 获取Outlook日历客户端 - 支持按需初始化
      let outlookClient = this.mcpManager.clients.get('outlook-calendar')

      // 如果客户端不存在，尝试按需初始化
      if (!outlookClient || !outlookClient.isConnected) {
        console.log('📅 [EMAIL_SERVICE] Outlook日历客户端未初始化，正在按需初始化...')
        try {
          outlookClient = await this.mcpManager.initializeOutlookCalendarMCP()
          console.log('✅ [EMAIL_SERVICE] Outlook日历客户端按需初始化成功')
        } catch (initError) {
          console.warn('📅 [EMAIL_SERVICE] Outlook日历MCP按需初始化失败，跳过同步:', initError.message)
          console.warn('📅 [EMAIL_SERVICE] 建议启动Microsoft Outlook后重试')
          return
        }
      }

      if (!outlookClient || !outlookClient.isConnected) {
        console.warn('📅 [EMAIL_SERVICE] Outlook日历MCP客户端仍不可用，跳过同步')
        console.warn('📅 [EMAIL_SERVICE] 客户端状态:', {
          exists: !!outlookClient,
          isConnected: outlookClient?.isConnected,
          configSource: outlookClient?.configSource
        })
        return
      }

      // 逐个同步待办事项
      for (const todo of newTodos) {
        try {
          // 构建日历事件参数
          const eventParams = this.buildCalendarEventParams(todo)

          console.log(`📅 [EMAIL_SERVICE] 创建日历事件: ${todo.subject}`)

          // 调用MCP工具创建日历事件
          const result = await this.mcpManager.callRealMCPTool('create_event', eventParams)

          if (result.success) {
            console.log(`📅 [EMAIL_SERVICE] 日历事件创建成功: ${todo.subject}`)

            // 标记待办事项已同步
            todo.syncedToCalendar = true
            todo.calendarEventId = result.eventId || result.event_id
            todo.syncTime = new Date().toISOString()

            // 保存更新后的待办事项
            this.saveTodosToStore()

            // 发送成功通知到渲染进程
            if (this.mainWindow && this.mainWindow.webContents) {
              this.mainWindow.webContents.send('todo-synced-to-calendar', {
                todoId: todo.id,
                subject: todo.subject,
                eventId: todo.calendarEventId,
                success: true
              })
            }
          } else {
            console.error(`📅 [EMAIL_SERVICE] 日历事件创建失败: ${todo.subject}`, result.error)
          }
        } catch (error) {
          console.error(`📅 [EMAIL_SERVICE] 同步待办事项异常: ${todo.subject}`, error)
        }
      }

      console.log(`📅 [EMAIL_SERVICE] 待办事项同步完成`)
    } catch (error) {
      console.error('📅 [EMAIL_SERVICE] 同步待办事项到日历时出错:', error)
    }
  }

  /**
   * 构建日历事件参数
   */
  buildCalendarEventParams(todo) {
    const now = new Date()

    // 根据待办事项类型设置不同的日程
    let startDateTime, endDateTime

    if (todo.dueDate) {
      // 如果有截止日期，设置为截止日期前的合适时间
      const dueTime = new Date(todo.dueDate)

      if (todo.todoType === 'meeting') {
        // 会议类型：设置为截止时间
        startDateTime = dueTime
        endDateTime = new Date(dueTime.getTime() + 60 * 60 * 1000) // 默认1小时
      } else {
        // 任务类型：设置为截止日期前1小时的提醒
        const reminderTime = new Date(dueTime.getTime() - 60 * 60 * 1000)
        startDateTime = reminderTime
        endDateTime = new Date(reminderTime.getTime() + 30 * 60 * 1000) // 30分钟提醒
      }
    } else {
      // 没有截止日期，设置为明天的合适时间
      const tomorrow = new Date(now.getTime() + 24 * 60 * 60 * 1000)

      if (todo.todoType === 'meeting') {
        // 会议设置为明天上午10点
        tomorrow.setHours(10, 0, 0, 0)
        startDateTime = tomorrow
        endDateTime = new Date(tomorrow.getTime() + 60 * 60 * 1000) // 1小时会议
      } else {
        // 任务设置为明天上午9点
        tomorrow.setHours(9, 0, 0, 0)
        startDateTime = tomorrow
        endDateTime = new Date(tomorrow.getTime() + 30 * 60 * 1000) // 30分钟提醒
      }
    }

    return {
      subject: `[待办] ${todo.subject}`,
      startDate: this.formatDate(startDateTime),
      startTime: this.formatTime(startDateTime),
      endDate: this.formatDate(endDateTime),
      endTime: this.formatTime(endDateTime),
      location: todo.location || '办公室',
      body: this.buildEventBody(todo),
      isMeeting: todo.todoType === 'meeting',
      attendees: todo.attendees || '',
      calendar: '' // 空字符串使用默认日历
    }
  }

  /**
   * 构建事件内容
   */
  buildEventBody(todo) {
    let body = `📧 待办事项同步\n\n`
    body += `📋 类型: ${this.getTodoTypeText(todo.todoType)}\n`
    body += `📝 描述: ${todo.todoDescription || '无描述'}\n`
    body += `📧 来源: ${todo.from || '未知'}\n`
    body += `⚡ 紧急程度: ${this.getUrgencyText(todo.urgency)}\n`

    if (todo.dueDate) {
      body += `⏰ 截止时间: ${new Date(todo.dueDate).toLocaleString('zh-CN')}\n`
    }

    body += `\n🤖 此事项由AI助手自动同步到日历`

    return body
  }

  /**
   * 格式化日期为 MM/DD/YYYY 格式
   */
  formatDate(date) {
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const year = date.getFullYear()
    return `${month}/${day}/${year}`
  }

  /**
   * 格式化时间为 HH:MM AM/PM 格式
   */
  formatTime(date) {
    return date.toLocaleString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    })
  }

  /**
   * 获取紧急程度文本
   */
  getUrgencyText(urgency) {
    const urgencyMap = {
      high: '🔴 紧急',
      medium: '🟡 普通',
      low: '🟢 较低'
    }
    return urgencyMap[urgency] || '🟡 普通'
  }

  scheduleReminders(todoEmails) {
    for (const email of todoEmails) {
      if (email.dueDate) {
        try {
          const dueTime = new Date(email.dueDate)
          const reminderTime = new Date(dueTime.getTime() - 30 * 60 * 1000) // 提前30分钟
          const now = new Date()

          if (reminderTime > now) {
            const delay = reminderTime.getTime() - now.getTime()

            const reminderId = setTimeout(() => {
              this.showReminder(email)
            }, delay)

            this.reminders.push({
              id: email.id,
              emailId: email.uid,
              timerId: reminderId,
              reminderTime: reminderTime.toISOString(),
              email: email
            })

            console.log(`📧 [EMAIL_SERVICE] 已设置提醒: ${email.subject} 将在 ${reminderTime.toLocaleString()} 提醒`)
          }
        } catch (error) {
          console.error('📧 [EMAIL_SERVICE] 设置提醒失败:', error)
        }
      }
    }
  }

  showReminder(email) {
    console.log(`📧 [EMAIL_SERVICE] 显示提醒: ${email.subject}`)

    // 构建提醒信息
    const reminderData = {
      id: email.id,
      subject: email.subject,
      from: email.from,
      todoDescription: email.todoDescription,
      urgency: email.urgency,
      dueDate: email.dueDate,
      timestamp: new Date().toISOString()
    }

    // 发送到渲染进程显示提醒弹框
    if (this.mainWindow && this.mainWindow.webContents) {
      this.mainWindow.webContents.send('show-email-reminder', reminderData)
    }

    // 系统通知
    if (Notification.isSupported()) {
      const notification = new Notification({
        title: '📋 待办事项提醒',
        body: `${email.subject}\n${email.todoDescription}`,
        icon: path.join(__dirname, '../public/assets/logo.ico'),
        sound: true,
        urgency: email.urgency === 'high' ? 'critical' : 'normal'
      })

      notification.show()

      // 点击通知时显示主窗口待办事项页面
      notification.on('click', () => {
        if (this.mainWindow) {
          this.mainWindow.show()
          this.mainWindow.focus()
          // 切换到待办事项页面
          this.mainWindow.webContents.send('navigate-to', 'todo')
        }
      })
    }

    // 语音播报提醒
    this.speakReminder(email)
  }

  async speakReminder(email) {
    try {
      // 构建语音文本
      let speechText = `犇犇提醒您，`

      if (email.urgency === 'high') {
        speechText += '紧急！'
      }

      speechText += `您有一个${this.getTodoTypeText(email.todoType)}需要处理：${email.subject}。`

      if (email.dueDate) {
        const dueTime = new Date(email.dueDate)
        const now = new Date()
        const timeDiff = dueTime.getTime() - now.getTime()

        if (timeDiff > 0) {
          const minutes = Math.floor(timeDiff / (1000 * 60))
          if (minutes < 60) {
            speechText += `还有${minutes}分钟到截止时间。`
          } else {
            const hours = Math.floor(minutes / 60)
            speechText += `还有${hours}小时到截止时间。`
          }
        } else {
          speechText += '已经超过截止时间了！'
        }
      }

      console.log(`📧 [EMAIL_SERVICE] 语音播报: ${speechText}`)

      // 发送语音播报请求到渲染进程
      if (this.mainWindow && this.mainWindow.webContents) {
        this.mainWindow.webContents.send('speak-text', {
          text: speechText,
          rate: 1.0,
          pitch: 1.0,
          volume: 1.0
        })
      }

    } catch (error) {
      console.error('📧 [EMAIL_SERVICE] 语音播报失败:', error)
    }
  }

  getTodoTypeText(todoType) {
    const typeMap = {
      meeting: '📅 会议',
      task: '📋 任务',
      deadline: '⏰ 截止',
      reply: '📨 回复',
      approval: '✅ 审批'
    }
    return typeMap[todoType] || '📄 其他'
  }

  startPolling() {
    // 检查是否有邮件配置
    const store = new Store()
    const emailConfig = store.get('emailConfig')

    if (!emailConfig || !emailConfig.user || !emailConfig.pass) {
      console.log('⚠️ [EMAIL_SERVICE] 未配置邮箱，跳过轮询启动')
      return
    }

    // 5分钟轮询一次
    this.pollingInterval = setInterval(async () => {
      console.log('📧 [EMAIL_SERVICE] 定时检查邮件...')
      await this.checkEmails(false)
    }, 5 * 60 * 1000) // 5分钟

    console.log('📧 [EMAIL_SERVICE] 已启动5分钟轮询')
  }

  stopPolling() {
    if (this.pollingInterval) {
      clearInterval(this.pollingInterval)
      this.pollingInterval = null
      console.log('📧 [EMAIL_SERVICE] 已停止轮询')
    }
  }

  cleanup() {
    console.log('📧 [EMAIL_SERVICE] 清理邮件服务...')

    // 停止轮询
    this.stopPolling()

    // 清理所有提醒定时器
    for (const reminder of this.reminders) {
      if (reminder.timerId) {
        clearTimeout(reminder.timerId)
      }
    }
    this.reminders = []

    // 移除所有IPC处理程序
    ipcMain.removeHandler('get-todo-list')
    ipcMain.removeHandler('clear-reminder')
    ipcMain.removeHandler('mark-todo-completed')
    ipcMain.removeHandler('delete-todo')
    ipcMain.removeHandler('update-todo')
    ipcMain.removeHandler('check-emails-manual')
    ipcMain.removeHandler('get-email-config')
    ipcMain.removeHandler('save-email-config')
    ipcMain.removeHandler('test-email-config')
    ipcMain.removeHandler('delete-email-config')

    // 重置初始化状态，确保下次初始化时会重新注册处理程序
    this.isInitialized = false

    console.log('📧 [EMAIL_SERVICE] 邮件服务已清理')
  }
}

module.exports = EmailService
