const { app } = require('electron')
const { join } = require('path')
const fs = require('fs')
const Store = require('electron-store')

// MCP SDK将通过动态导入加载
let StdioClientTransport, Client, ListToolsRequestSchema, CallToolRequestSchema

// MCP客户端管理器类
class MCPClientManager {
  constructor() {
    this.clients = new Map()
    this.servers = new Map()
    this.initialized = false
    this.wordMCPProcess = null
    // 用户配置
    this.userConfig = null
  }

  // 获取用户配置
  getUserConfig() {
    if (!this.userConfig) {
      try {
        // 尝试从存储中读取配置
        const configPath = join(app.getPath('userData'), 'config.json')
        if (fs.existsSync(configPath)) {
          const configData = fs.readFileSync(configPath, 'utf8')
          this.userConfig = JSON.parse(configData)
          console.log('📋 已从文件加载用户配置')
        } else {
          console.log('📋 用户配置文件不存在，使用默认配置')
          this.userConfig = {}
        }
      } catch (error) {
        console.error('❌ 读取用户配置失败:', error)
        this.userConfig = {}
      }
    }
    return this.userConfig
  }

  // 保存用户配置
  saveUserConfig(config) {
    try {
      this.userConfig = config
      const configPath = join(app.getPath('userData'), 'config.json')
      fs.writeFileSync(configPath, JSON.stringify(config, null, 2), 'utf8')
      console.log('📋 用户配置已保存到文件')
      return true
    } catch (error) {
      console.error('❌ 保存用户配置失败:', error)
      return false
    }
  }

  // 发送状态更新到主窗口
  sendStatusUpdate(message) {
    if (global.mainWindow && !global.mainWindow.isDestroyed()) {
      global.mainWindow.webContents.send('status-message', message)
    }
  }

  async initialize() {
    if (this.initialized) return

    try {
      console.log('🚀 ====== 开始初始化MCP服务器 ======')
      console.log('🚀 当前时间:', new Date().toISOString())

      // 发送初始化开始状态
      this.sendStatusUpdate('开始初始化MCP服务...')

      // 初始化文件系统MCP服务器
      console.log('🚀 [1/5] 初始化文件系统MCP服务器...')
      this.sendStatusUpdate('正在初始化文件系统服务...')

      try {
        await this.initializeFilesystemMCP()
        console.log('✅ [1/5] 文件系统MCP服务器初始化完成')
        this.sendStatusUpdate('文件系统服务初始化完成')
      } catch (error) {
        console.error('❌ [1/5] 文件系统MCP服务器初始化失败:', error.message)
        this.sendStatusUpdate('文件系统服务初始化失败，将使用有限功能继续')
        // 继续执行，不要中断整体流程
      }

      // 初始化系统操作MCP（模拟）
      console.log('🚀 [2/5] 初始化系统操作MCP服务器...')
      this.sendStatusUpdate('正在初始化系统操作服务...')

      try {
        await this.initializeSystemMCP()
        console.log('✅ [2/5] 系统操作MCP服务器初始化完成')
        this.sendStatusUpdate('系统操作服务初始化完成')
      } catch (error) {
        console.error('❌ [2/5] 系统操作MCP服务器初始化失败:', error.message)
        this.sendStatusUpdate('系统操作服务初始化失败，将使用有限功能继续')
        // 继续执行，不要中断整体流程
      }

      // 初始化浏览器MCP服务器
      console.log('🚀 [3/5] 初始化浏览器MCP服务器...')
      this.sendStatusUpdate('正在初始化浏览器控制服务...')

      try {
        await this.initializeBrowserMCP()
        console.log('✅ [3/5] 浏览器MCP服务器初始化完成')
        this.sendStatusUpdate('浏览器控制服务初始化完成')
      } catch (error) {
        console.error('❌ [3/5] 浏览器MCP服务器初始化失败:', error.message)
        this.sendStatusUpdate('浏览器控制服务初始化失败，将使用有限功能继续')
        // 继续执行，不要中断整体流程
      }

      // 检查是否有邮件配置，决定是否初始化Email MCP服务器
      const store = new Store()
      const emailConfig = store.get('emailConfig')
      
      if (emailConfig && emailConfig.user && emailConfig.pass) {
        console.log('🚀 [4/5] 初始化Email MCP服务器...')
        this.sendStatusUpdate('正在初始化邮件服务...')

        try {
          await this.initializeEmailMCP()
          console.log('✅ [4/5] Email MCP服务器初始化完成')
          this.sendStatusUpdate('邮件服务初始化完成')
        } catch (error) {
          console.error('❌ [4/5] Email MCP服务器初始化失败:', error.message)
          this.sendStatusUpdate('邮件服务初始化失败，将使用有限功能继续')
          // 继续执行，不要中断整体流程
        }
      } else {
        console.log('⚠️ [4/5] 未配置邮箱，跳过Email MCP服务器初始化')
        this.sendStatusUpdate('未配置邮箱，跳过邮件服务初始化')
      }

      // 初始化Office Word MCP服务器
      console.log('🚀 [5/5] 初始化Office Word MCP服务器...')
      this.sendStatusUpdate('正在初始化Office Word服务...')

      try {
        await this.initializeOfficeMCP()
        console.log('✅ [5/5] Office Word MCP服务器初始化完成')
        this.sendStatusUpdate('Office Word服务初始化完成')
      } catch (error) {
        console.error('❌ [5/5] Office Word MCP服务器初始化失败:', error.message)
        this.sendStatusUpdate('Office Word服务初始化失败，将使用有限功能继续')
        // 继续执行，不要中断整体流程
      }

      this.initialized = true
      console.log('🎉 ====== MCP服务器初始化完成 ======')
      console.log('🎉 已初始化的服务:', Array.from(this.clients.keys()))
      this.sendStatusUpdate('所有MCP服务初始化完成')

    } catch (error) {
      console.error('❌ MCP服务器初始化失败:', error)
      this.sendStatusUpdate('MCP服务初始化失败，应用将以有限功能运行')
      throw error
    }
  }

  // 初始化文件系统MCP服务器
  async initializeFilesystemMCP() {
    try {
      console.log('📁 正在初始化文件系统MCP服务器...')

      // 获取桌面路径作为默认目录
      const desktopDir = app.getPath('desktop')
      console.log('📁 允许访问的目录:', desktopDir)

      // 动态导入MCP SDK
      if (!StdioClientTransport) {
        console.log('📦 动态加载MCP SDK...')
        try {
          // 从正确的模块导入 StdioClientTransport
          const stdioModule = await import('@modelcontextprotocol/sdk/client/stdio.js')
          StdioClientTransport = stdioModule.StdioClientTransport

          // 从 client/index.js 导入 Client
          const clientModule = await import('@modelcontextprotocol/sdk/client/index.js')
          Client = clientModule.Client

          // 从 types 模块导入 schema
          const typesModule = await import('@modelcontextprotocol/sdk/types.js')
          ListToolsRequestSchema = typesModule.ListToolsRequestSchema
          CallToolRequestSchema = typesModule.CallToolRequestSchema

          console.log('✅ MCP SDK加载成功')
        } catch (importError) {
          console.error('❌ MCP SDK导入失败:', importError)
          throw importError
        }
      }

      // 创建文件系统MCP客户端
      const transport = new StdioClientTransport({
        command: 'npx',
        args: ['-y', '@modelcontextprotocol/server-filesystem', desktopDir],
        env: {
          ...process.env
        }
      })

      const client = new Client({
        name: 'nezha-filesystem-client',
        version: '1.0.0'
      }, {
        capabilities: {}
      })

      // 连接到MCP服务器
      await client.connect(transport)
      console.log('✅ 已连接到文件系统MCP服务器')

      // 获取可用工具列表
      console.log('📋 正在获取工具列表...')
      const tools = await client.listTools()

      console.log('📋 工具列表获取成功:', tools)
      console.log('📋 可用的文件系统工具:', tools.tools?.map(t => t.name) || [])

      const filesystemClient = {
        name: 'filesystem',
        isConnected: true,
        defaultDir: desktopDir,
        supportedFormats: ['.txt', '.md', '.json', '.js', '.py', '.html', '.css'],
        mcpClient: client,
        mcpTransport: transport,
        availableTools: tools.tools,
        isRealMCP: true,
        configSource: 'npx @modelcontextprotocol/server-filesystem'
      }

      this.clients.set('filesystem', filesystemClient)
      console.log('✅ 文件系统MCP客户端初始化完成')

      return filesystemClient
    } catch (error) {
      console.error('❌ 文件系统MCP初始化失败:', error)
      
      // 创建模拟客户端作为后备
      const fallbackClient = {
        name: 'filesystem',
        isConnected: false,
        defaultDir: app.getPath('desktop'),
        supportedFormats: ['.txt', '.md', '.json', '.js', '.py', '.html', '.css'],
        mcpClient: {
          listTools: async () => {
            return {
              tools: [
                { name: 'read_file', description: '读取文件 (模拟)' },
                { name: 'write_file', description: '写入文件 (模拟)' },
                { name: 'list_directory', description: '列出目录 (模拟)' }
              ]
            }
          }
        },
        availableTools: [
          { name: 'read_file', description: '读取文件 (模拟)' },
          { name: 'write_file', description: '写入文件 (模拟)' },
          { name: 'list_directory', description: '列出目录 (模拟)' }
        ],
        isRealMCP: false,
        configSource: '模拟后备',
        lastConnected: new Date().toISOString(),
        error: error.message
      }

      this.clients.set('filesystem', fallbackClient)
      console.log('✅ 模拟文件系统客户端创建完成')

      throw error
    }
  }

  // 初始化系统操作MCP（模拟）
  async initializeSystemMCP() {
    try {
      console.log('🖥️ 正在初始化系统操作MCP服务器（模拟）...')

      // 创建模拟的系统操作客户端
      const systemClient = {
        name: 'system',
        isConnected: true,
        mcpClient: {
          listTools: async () => {
            return {
              tools: [
                { name: 'run_command', description: '执行系统命令' },
                { name: 'get_system_info', description: '获取系统信息' },
                { name: 'list_processes', description: '列出运行中的进程' },
                { name: 'kill_process', description: '终止进程' }
              ]
            }
          }
        },
        availableTools: [
          { name: 'run_command', description: '执行系统命令' },
          { name: 'get_system_info', description: '获取系统信息' },
          { name: 'list_processes', description: '列出运行中的进程' },
          { name: 'kill_process', description: '终止进程' }
        ],
        isRealMCP: false,
        configSource: '内置模拟',
        lastConnected: new Date().toISOString()
      }

      this.clients.set('system', systemClient)
      console.log('✅ 系统操作MCP客户端初始化完成（模拟）')

      return systemClient
    } catch (error) {
      console.error('❌ 系统操作MCP初始化失败:', error)
      throw error
    }
  }

  // 初始化浏览器MCP服务器
  async initializeBrowserMCP() {
    try {
      console.log('🌐 正在初始化浏览器MCP服务器...')

      // 动态导入MCP SDK（如果还没有导入）
      if (!StdioClientTransport) {
        console.log('📦 动态加载MCP SDK...')
        try {
          const stdioModule = await import('@modelcontextprotocol/sdk/client/stdio.js')
          StdioClientTransport = stdioModule.StdioClientTransport

          const clientModule = await import('@modelcontextprotocol/sdk/client/index.js')
          Client = clientModule.Client

          const typesModule = await import('@modelcontextprotocol/sdk/types.js')
          ListToolsRequestSchema = typesModule.ListToolsRequestSchema
          CallToolRequestSchema = typesModule.CallToolRequestSchema

          console.log('✅ MCP SDK加载成功')
        } catch (importError) {
          console.error('❌ MCP SDK导入失败:', importError)
          throw importError
        }
      }

      // 创建浏览器MCP客户端
      const transport = new StdioClientTransport({
        command: 'npx',
        args: ['-y', '@modelcontextprotocol/server-puppeteer'],
        env: {
          ...process.env
        }
      })

      const client = new Client({
        name: 'nezha-browser-client',
        version: '1.0.0'
      }, {
        capabilities: {}
      })

      // 连接到MCP服务器
      await client.connect(transport)
      console.log('✅ 已连接到浏览器MCP服务器')

      // 获取可用工具列表
      console.log('📋 正在获取浏览器工具列表...')
      const tools = await client.listTools()

      console.log('📋 浏览器工具列表获取成功:', tools)
      console.log('📋 可用的浏览器工具:', tools.tools?.map(t => t.name) || [])

      const browserClient = {
        name: 'browser',
        isConnected: true,
        mcpClient: client,
        mcpTransport: transport,
        availableTools: tools.tools,
        isRealMCP: true,
        configSource: 'npx @modelcontextprotocol/server-puppeteer',
        lastConnected: new Date().toISOString()
      }

      this.clients.set('browser', browserClient)
      console.log('✅ 浏览器MCP客户端初始化完成')

      return browserClient
    } catch (error) {
      console.error('❌ 浏览器MCP初始化失败:', error)

      // 创建模拟客户端作为后备
      const fallbackClient = {
        name: 'browser',
        isConnected: false,
        mcpClient: {
          listTools: async () => {
            return {
              tools: [
                { name: 'puppeteer_navigate', description: '导航到网页 (模拟)' },
                { name: 'puppeteer_screenshot', description: '截取网页截图 (模拟)' },
                { name: 'puppeteer_click', description: '点击元素 (模拟)' },
                { name: 'puppeteer_type', description: '输入文本 (模拟)' }
              ]
            }
          }
        },
        availableTools: [
          { name: 'puppeteer_navigate', description: '导航到网页 (模拟)' },
          { name: 'puppeteer_screenshot', description: '截取网页截图 (模拟)' },
          { name: 'puppeteer_click', description: '点击元素 (模拟)' },
          { name: 'puppeteer_type', description: '输入文本 (模拟)' }
        ],
        isRealMCP: false,
        configSource: '模拟后备',
        lastConnected: new Date().toISOString(),
        error: error.message
      }

      this.clients.set('browser', fallbackClient)
      console.log('✅ 模拟浏览器客户端创建完成')

      throw error
    }
  }

  // 初始化Email MCP服务器
  async initializeEmailMCP() {
    try {
      console.log('📧 正在初始化Email MCP服务器...')

      // 获取邮件配置
      const store = new Store()
      const emailConfig = store.get('emailConfig')

      if (!emailConfig || !emailConfig.user || !emailConfig.pass) {
        throw new Error('邮件配置不完整')
      }

      console.log('📧 邮件配置:', {
        user: emailConfig.user,
        server: emailConfig.server,
        port: emailConfig.port,
        hasPassword: !!emailConfig.pass
      })

      // 动态导入MCP SDK（如果还没有导入）
      if (!StdioClientTransport) {
        console.log('📦 动态加载MCP SDK...')
        try {
          const stdioModule = await import('@modelcontextprotocol/sdk/client/stdio.js')
          StdioClientTransport = stdioModule.StdioClientTransport

          const clientModule = await import('@modelcontextprotocol/sdk/client/index.js')
          Client = clientModule.Client

          const typesModule = await import('@modelcontextprotocol/sdk/types.js')
          ListToolsRequestSchema = typesModule.ListToolsRequestSchema
          CallToolRequestSchema = typesModule.CallToolRequestSchema

          console.log('✅ MCP SDK加载成功')
        } catch (importError) {
          console.error('❌ MCP SDK导入失败:', importError)
          throw importError
        }
      }

      // 创建Email MCP客户端
      const transport = new StdioClientTransport({
        command: 'uv',
        args: ['run', '--directory', './mcp-servers/email-server', 'email-server'],
        env: {
          ...process.env,
          EMAIL_USER: emailConfig.user,
          EMAIL_PASS: emailConfig.pass,
          EMAIL_SERVER: emailConfig.server,
          EMAIL_PORT: emailConfig.port.toString()
        }
      })

      const client = new Client({
        name: 'nezha-email-client',
        version: '1.0.0'
      }, {
        capabilities: {}
      })

      // 连接到MCP服务器
      await client.connect(transport)
      console.log('✅ 已连接到Email MCP服务器')

      // 获取可用工具列表
      console.log('📋 正在获取邮件工具列表...')
      const tools = await client.listTools()

      console.log('📋 邮件工具列表获取成功:', tools)
      console.log('📋 可用的邮件工具:', tools.tools?.map(t => t.name) || [])

      const emailClient = {
        name: 'email-server',
        isConnected: true,
        mcpClient: client,
        mcpTransport: transport,
        availableTools: tools.tools,
        isRealMCP: true,
        configSource: 'uv run email-server',
        lastConnected: new Date().toISOString(),
        emailConfig: {
          user: emailConfig.user,
          server: emailConfig.server,
          port: emailConfig.port
        }
      }

      this.clients.set('email-server', emailClient)
      console.log('✅ Email MCP客户端初始化完成')

      return emailClient
    } catch (error) {
      console.error('❌ Email MCP初始化失败:', error)

      // 创建模拟客户端作为后备
      const fallbackClient = {
        name: 'email-server',
        isConnected: false,
        mcpClient: {
          listTools: async () => {
            return {
              tools: [
                { name: 'list_email', description: '列出邮件 (模拟)' },
                { name: 'send_email', description: '发送邮件 (模拟)' },
                { name: 'mark_email_as_read', description: '标记邮件为已读 (模拟)' }
              ]
            }
          }
        },
        availableTools: [
          { name: 'list_email', description: '列出邮件 (模拟)' },
          { name: 'send_email', description: '发送邮件 (模拟)' },
          { name: 'mark_email_as_read', description: '标记邮件为已读 (模拟)' }
        ],
        isRealMCP: false,
        configSource: '模拟后备',
        lastConnected: new Date().toISOString(),
        error: error.message
      }

      this.clients.set('email-server', fallbackClient)
      console.log('✅ 模拟邮件客户端创建完成')

      throw error
    }
  }

  // 初始化Office MCP服务器
  async initializeOfficeMCP() {
    try {
      console.log('📄 正在初始化Office Word MCP服务器...')

      // 使用桌面作为默认保存目录
      const os = require('os')
      const desktopDir = join(os.homedir(), 'Desktop')

      if (!fs.existsSync(desktopDir)) {
        fs.mkdirSync(desktopDir, { recursive: true })
        console.log('创建桌面目录:', desktopDir)
      }

      // 读取MCP配置文件
      const isDev = process.env.NODE_ENV === 'development'
      const mcpConfigPath = isDev
        ? join(process.cwd(), 'mcp-config.json')
        : join(process.resourcesPath, 'mcp-config.json')

      console.log('📋 读取MCP配置文件:', mcpConfigPath)

      if (!fs.existsSync(mcpConfigPath)) {
        console.error('❌ MCP配置文件不存在:', mcpConfigPath)
        throw new Error('MCP配置文件不存在')
      }

      const mcpConfig = JSON.parse(fs.readFileSync(mcpConfigPath, 'utf8'))
      console.log('📋 MCP配置内容:', JSON.stringify(mcpConfig, null, 2))

      // 获取office-bot-mcp配置
      const officeBotConfig = mcpConfig.mcpServers['office-bot-mcp']
      if (!officeBotConfig) {
        console.error('❌ 未找到office-bot-mcp配置')
        throw new Error('未找到office-bot-mcp配置')
      }

      console.log('🔧 office-bot-mcp配置:', officeBotConfig)

      // 动态导入MCP SDK（如果还没有导入）
      if (!StdioClientTransport) {
        console.log('📦 动态加载MCP SDK...')
        try {
          const stdioModule = await import('@modelcontextprotocol/sdk/client/stdio.js')
          StdioClientTransport = stdioModule.StdioClientTransport

          const clientModule = await import('@modelcontextprotocol/sdk/client/index.js')
          Client = clientModule.Client

          const typesModule = await import('@modelcontextprotocol/sdk/types.js')
          ListToolsRequestSchema = typesModule.ListToolsRequestSchema
          CallToolRequestSchema = typesModule.CallToolRequestSchema

          console.log('✅ MCP SDK加载成功')
        } catch (importError) {
          console.error('❌ MCP SDK导入失败:', importError)
          throw importError
        }
      }

      // 解析命令和参数的绝对路径
      const resolvedCommand = isDev
        ? officeBotConfig.command
        : join(process.resourcesPath, officeBotConfig.command)

      const resolvedArgs = officeBotConfig.args.map(arg =>
        isDev ? arg : join(process.resourcesPath, arg)
      )

      console.log('🚀 启动Office MCP服务器:')
      console.log('  - 解析后命令:', resolvedCommand)
      console.log('  - 解析后参数:', resolvedArgs)

      // 验证Python可执行文件是否存在
      if (!fs.existsSync(resolvedCommand)) {
        console.error('❌ Python可执行文件不存在:', resolvedCommand)
        throw new Error(`Python可执行文件不存在: ${resolvedCommand}`)
      }

      // 创建Office MCP客户端
      const transport = new StdioClientTransport({
        command: resolvedCommand,
        args: resolvedArgs,
        env: {
          ...process.env,
          PYTHONPATH: isDev ? process.cwd() : process.resourcesPath,
          PYTHONUNBUFFERED: '1'
        }
      })

      const client = new Client({
        name: 'nezha-office-client',
        version: '1.0.0'
      }, {
        capabilities: {}
      })

      // 连接到MCP服务器
      await client.connect(transport)
      console.log('✅ 已连接到Office Word MCP服务器')

      // 获取可用工具列表
      console.log('📋 正在获取Office工具列表...')
      const tools = await client.listTools()

      console.log('📋 Office工具列表获取成功:', tools)
      console.log('📋 可用的Office工具:', tools.tools?.map(t => t.name) || [])

      const officeClient = {
        name: 'office-word',
        isConnected: true,
        defaultDir: desktopDir,
        mcpClient: client,
        mcpTransport: transport,
        availableTools: tools.tools,
        isRealMCP: true,
        configSource: 'office-bot-mcp',
        lastConnected: new Date().toISOString()
      }

      this.clients.set('office-word', officeClient)
      console.log('✅ Office Word MCP客户端初始化完成')

      return officeClient
    } catch (error) {
      console.error('❌ Office MCP初始化失败:', error)

      // 创建模拟客户端作为后备
      const fallbackClient = {
        name: 'office-word',
        isConnected: false,
        defaultDir: join(require('os').homedir(), 'Desktop'),
        mcpClient: {
          listTools: async () => {
            return {
              tools: [
                { name: 'create_word_document', description: '创建Word文档 (模拟)' },
                { name: 'read_word_document', description: '读取Word文档 (模拟)' },
                { name: 'update_word_document', description: '更新Word文档 (模拟)' }
              ]
            }
          }
        },
        availableTools: [
          { name: 'create_word_document', description: '创建Word文档 (模拟)' },
          { name: 'read_word_document', description: '读取Word文档 (模拟)' },
          { name: 'update_word_document', description: '更新Word文档 (模拟)' }
        ],
        isRealMCP: false,
        configSource: '模拟后备',
        lastConnected: new Date().toISOString(),
        error: error.message
      }

      this.clients.set('office-word', fallbackClient)
      console.log('✅ 模拟Office客户端创建完成')

      throw error
    }
  }

  // 按需初始化Outlook日历MCP
  async initializeOutlookCalendarMCP() {
    try {
      console.log('📅 正在按需初始化Outlook日历MCP服务器...')

      // 检查是否已经初始化
      const existingClient = this.clients.get('outlook-calendar')
      if (existingClient && existingClient.isConnected) {
        console.log('📅 Outlook日历MCP客户端已存在且已连接')
        return existingClient
      }

      // 动态导入MCP SDK（如果还没有导入）
      if (!StdioClientTransport) {
        console.log('📦 动态加载MCP SDK...')
        try {
          const stdioModule = await import('@modelcontextprotocol/sdk/client/stdio.js')
          StdioClientTransport = stdioModule.StdioClientTransport

          const clientModule = await import('@modelcontextprotocol/sdk/client/index.js')
          Client = clientModule.Client

          const typesModule = await import('@modelcontextprotocol/sdk/types.js')
          ListToolsRequestSchema = typesModule.ListToolsRequestSchema
          CallToolRequestSchema = typesModule.CallToolRequestSchema

          console.log('✅ MCP SDK加载成功')
        } catch (importError) {
          console.error('❌ MCP SDK导入失败:', importError)
          throw importError
        }
      }

      // 创建Outlook日历MCP客户端
      const transport = new StdioClientTransport({
        command: 'uv',
        args: ['run', '--directory', './mcp-servers/outlook-calendar', 'outlook-calendar'],
        env: {
          ...process.env
        }
      })

      const client = new Client({
        name: 'nezha-outlook-calendar-client',
        version: '1.0.0'
      }, {
        capabilities: {}
      })

      // 连接到MCP服务器
      await client.connect(transport)
      console.log('✅ 已连接到Outlook日历MCP服务器')

      // 获取可用工具列表
      console.log('📋 正在获取Outlook日历工具列表...')
      const tools = await client.listTools()

      console.log('📋 Outlook日历工具列表获取成功:', tools)
      console.log('📋 可用的Outlook日历工具:', tools.tools?.map(t => t.name) || [])

      const outlookCalendarClient = {
        name: 'outlook-calendar',
        isConnected: true,
        mcpClient: client,
        mcpTransport: transport,
        availableTools: tools.tools,
        isRealMCP: true,
        configSource: 'uv run outlook-calendar',
        lastConnected: new Date().toISOString()
      }

      this.clients.set('outlook-calendar', outlookCalendarClient)
      console.log('✅ Outlook日历MCP客户端按需初始化完成')

      return outlookCalendarClient
    } catch (error) {
      console.error('❌ Outlook日历MCP按需初始化失败:', error)

      // 创建模拟客户端作为后备
      const fallbackClient = {
        name: 'outlook-calendar',
        isConnected: false,
        mcpClient: {
          listTools: async () => {
            return {
              tools: [
                { name: 'create_event', description: '创建日历事件 (模拟)' },
                { name: 'list_events', description: '列出日历事件 (模拟)' },
                { name: 'update_event', description: '更新日历事件 (模拟)' },
                { name: 'delete_event', description: '删除日历事件 (模拟)' }
              ]
            }
          }
        },
        availableTools: [
          { name: 'create_event', description: '创建日历事件 (模拟)' },
          { name: 'list_events', description: '列出日历事件 (模拟)' },
          { name: 'update_event', description: '更新日历事件 (模拟)' },
          { name: 'delete_event', description: '删除日历事件 (模拟)' }
        ],
        isRealMCP: false,
        configSource: '模拟后备',
        lastConnected: new Date().toISOString(),
        error: error.message
      }

      this.clients.set('outlook-calendar', fallbackClient)
      console.log('✅ 模拟Outlook日历客户端创建完成')

      throw error
    }
  }

  // 调用真实的MCP工具
  async callRealMCPTool(toolName, args, clientName = null) {
    try {
      console.log(`🔧 [MCP_CALL] 调用MCP工具: ${toolName}`)
      console.log(`🔧 [MCP_CALL] 参数:`, args)
      console.log(`🔧 [MCP_CALL] 指定客户端:`, clientName)

      // 如果指定了客户端名称，直接使用该客户端
      if (clientName) {
        const client = this.clients.get(clientName)
        if (!client) {
          throw new Error(`指定的MCP客户端不存在: ${clientName}`)
        }
        if (!client.isConnected || !client.mcpClient) {
          throw new Error(`指定的MCP客户端未连接: ${clientName}`)
        }

        console.log(`🔧 [MCP_CALL] 使用指定客户端: ${clientName}`)
        const result = await client.mcpClient.callTool({
          name: toolName,
          arguments: args
        })

        console.log(`✅ [MCP_CALL] 工具调用成功: ${toolName}`)
        return this.processMCPResult(result)
      }

      // 自动查找支持该工具的客户端
      for (const [name, client] of this.clients.entries()) {
        if (!client.isConnected || !client.mcpClient) {
          console.log(`⏭️ [MCP_CALL] 跳过未连接的客户端: ${name}`)
          continue
        }

        // 检查客户端是否支持该工具
        const supportsTool = client.availableTools?.some(tool => tool.name === toolName)
        if (!supportsTool) {
          console.log(`⏭️ [MCP_CALL] 客户端 ${name} 不支持工具 ${toolName}`)
          continue
        }

        console.log(`🔧 [MCP_CALL] 使用客户端 ${name} 调用工具 ${toolName}`)

        try {
          const result = await client.mcpClient.callTool({
            name: toolName,
            arguments: args
          })

          console.log(`✅ [MCP_CALL] 工具调用成功: ${toolName} (客户端: ${name})`)
          return this.processMCPResult(result)
        } catch (clientError) {
          console.error(`❌ [MCP_CALL] 客户端 ${name} 调用失败:`, clientError)
          continue // 尝试下一个客户端
        }
      }

      throw new Error(`没有找到支持工具 ${toolName} 的MCP客户端`)
    } catch (error) {
      console.error(`❌ [MCP_CALL] MCP工具调用失败: ${toolName}`, error)
      return {
        success: false,
        error: error.message,
        toolName,
        args
      }
    }
  }

  // 处理MCP结果
  processMCPResult(result) {
    try {
      if (!result || !result.content) {
        return {
          success: false,
          error: 'MCP返回结果格式错误'
        }
      }

      // 提取文本内容
      const textContent = result.content
        .filter(item => item.type === 'text')
        .map(item => item.text)
        .join('\n')

      if (!textContent) {
        return {
          success: false,
          error: 'MCP返回结果中没有文本内容'
        }
      }

      // 尝试解析JSON
      try {
        const parsedResult = JSON.parse(textContent)
        return {
          success: true,
          ...parsedResult
        }
      } catch (parseError) {
        // 如果不是JSON，直接返回文本
        return {
          success: true,
          message: textContent,
          rawContent: textContent
        }
      }
    } catch (error) {
      console.error('❌ 处理MCP结果失败:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  // 获取所有客户端状态
  getClientsStatus() {
    const status = {}
    for (const [name, client] of this.clients.entries()) {
      status[name] = {
        name: client.name,
        isConnected: client.isConnected,
        isRealMCP: client.isRealMCP,
        configSource: client.configSource,
        lastConnected: client.lastConnected,
        availableTools: client.availableTools?.map(t => t.name) || [],
        error: client.error
      }
    }
    return status
  }

  // 获取特定客户端
  getClient(name) {
    return this.clients.get(name)
  }

  // 检查客户端是否支持工具
  clientSupportsTools(clientName, toolNames) {
    const client = this.clients.get(clientName)
    if (!client || !client.availableTools) {
      return false
    }

    const availableToolNames = client.availableTools.map(t => t.name)
    return toolNames.every(toolName => availableToolNames.includes(toolName))
  }

  // 清理所有客户端连接
  async cleanup() {
    console.log('🧹 开始清理MCP客户端连接...')

    for (const [name, client] of this.clients.entries()) {
      try {
        if (client.mcpTransport && typeof client.mcpTransport.close === 'function') {
          await client.mcpTransport.close()
          console.log(`✅ 已关闭 ${name} 客户端连接`)
        }
      } catch (error) {
        console.error(`❌ 关闭 ${name} 客户端连接失败:`, error)
      }
    }

    // 清理Word MCP进程
    if (this.wordMCPProcess) {
      try {
        this.wordMCPProcess.kill()
        console.log('✅ 已终止Word MCP进程')
      } catch (error) {
        console.error('❌ 终止Word MCP进程失败:', error)
      }
    }

    this.clients.clear()
    this.servers.clear()
    this.initialized = false
    console.log('✅ MCP客户端清理完成')
  }
}

module.exports = MCPClientManager
