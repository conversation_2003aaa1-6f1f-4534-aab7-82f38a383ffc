const { app, BrowserWindow, <PERSON><PERSON>, Tray, ipc<PERSON>ain, dialog, shell, Notification } = require('electron')
const { join } = require('path')
const fs = require('fs')
const Store = require('electron-store')

// 导入其他服务模块
const MCPClientManager = require('./mcp-manager')
const EmailService = require('./email-service')
const OutlookCalendarService = require('./calendar-service')
const { setupConsoleForwarding, setupWindowsSpawnInterception, updateKnowledgeConfig } = require('./utils')
const { 
  initKnowledgeDatabase, 
  searchKnowledge, 
  getKnowledgeStats, 
  indexKnowledgeDocument, 
  clearKnowledgeBase, 
  rebuildKnowledgeBase, 
  getKnowledgeDocumentFiles 
} = require('./knowledge-service')

class AppManager {
  constructor() {
    this.mainWindow = null
    this.floatingWindow = null
    this.tray = null
    this.isLoggedIn = false
    this.hasShownTrayNotification = false
    this.mcpManager = new MCPClientManager()
    this.emailService = null
    this.outlookCalendarService = null
    this.servicesInitialized = false
  }

  async initialize() {
    console.log('🚀 开始初始化应用管理器...')

    // 设置Windows子进程窗口隐藏
    setupWindowsSpawnInterception()

    // 创建主窗口
    await this.createMainWindow()

    // 设置console转发
    setupConsoleForwarding(this.mainWindow)

    // 创建系统托盘
    this.createTray()

    // 设置应用菜单
    this.setupMenu()

    // 设置IPC处理程序
    this.setupIPC()

    // 初始化知识库
    await this.initializeKnowledge()

    // 初始化MCP服务
    await this.initializeMCPServices()

    // 初始化其他服务
    await this.initializeServices()

    console.log('✅ 应用管理器初始化完成')
  }

  async createMainWindow() {
    console.log('🪟 创建主窗口...')

    this.mainWindow = new BrowserWindow({
      width: 1200,
      height: 800,
      minWidth: 800,
      minHeight: 600,
      webPreferences: {
        nodeIntegration: true,
        contextIsolation: false,
        enableRemoteModule: true,
        webSecurity: false
      },
      icon: join(__dirname, '../public/assets/logo.ico'),
      show: false, // 初始不显示，等待ready-to-show事件
      titleBarStyle: 'default'
    })

    // 设置全局引用
    global.mainWindow = this.mainWindow

    // 加载应用
    const isDev = process.env.NODE_ENV === 'development'
    if (isDev) {
      this.mainWindow.loadURL('http://localhost:3000')
      // 开发模式下打开开发者工具
      // this.mainWindow.webContents.openDevTools()
    } else {
      this.mainWindow.loadFile(join(__dirname, '../build/index.html'))
    }

    // 窗口准备显示时显示窗口
    this.mainWindow.once('ready-to-show', () => {
      console.log('🪟 主窗口准备就绪，显示窗口')
      this.mainWindow.show()
      
      // 检查登录状态
      this.checkLoginStatus()
    })

    // 窗口关闭时隐藏到托盘
    this.mainWindow.on('close', (event) => {
      if (!app.isQuiting) {
        event.preventDefault()
        this.mainWindow.hide()
        
        // 显示托盘通知（仅第一次）
        if (!this.hasShownTrayNotification) {
          this.showTrayNotification()
          this.hasShownTrayNotification = true
        }
      }
    })

    console.log('✅ 主窗口创建完成')
  }

  createTray() {
    console.log('🔔 创建系统托盘...')

    const trayIconPath = join(__dirname, '../public/assets/logo.ico')
    this.tray = new Tray(trayIconPath)

    const contextMenu = Menu.buildFromTemplate([
      {
        label: '显示主窗口',
        click: () => {
          this.mainWindow.show()
          this.mainWindow.focus()
        }
      },
      {
        label: '退出',
        click: () => {
          app.isQuiting = true
          app.quit()
        }
      }
    ])

    this.tray.setToolTip('犇犇AI助手')
    this.tray.setContextMenu(contextMenu)

    // 双击托盘图标显示主窗口
    this.tray.on('double-click', () => {
      this.mainWindow.show()
      this.mainWindow.focus()
    })

    console.log('✅ 系统托盘创建完成')
  }

  setupMenu() {
    // 在macOS上设置应用菜单
    if (process.platform === 'darwin') {
      const template = [
        {
          label: app.getName(),
          submenu: [
            { role: 'about' },
            { type: 'separator' },
            { role: 'services' },
            { type: 'separator' },
            { role: 'hide' },
            { role: 'hideothers' },
            { role: 'unhide' },
            { type: 'separator' },
            { role: 'quit' }
          ]
        },
        {
          label: 'Edit',
          submenu: [
            { role: 'undo' },
            { role: 'redo' },
            { type: 'separator' },
            { role: 'cut' },
            { role: 'copy' },
            { role: 'paste' }
          ]
        },
        {
          label: 'View',
          submenu: [
            { role: 'reload' },
            { role: 'forceReload' },
            { role: 'toggleDevTools' },
            { type: 'separator' },
            { role: 'resetZoom' },
            { role: 'zoomIn' },
            { role: 'zoomOut' },
            { type: 'separator' },
            { role: 'togglefullscreen' }
          ]
        },
        {
          label: 'Window',
          submenu: [
            { role: 'minimize' },
            { role: 'close' }
          ]
        }
      ]

      const menu = Menu.buildFromTemplate(template)
      Menu.setApplicationMenu(menu)
    } else {
      // 在Windows和Linux上隐藏菜单栏
      Menu.setApplicationMenu(null)
    }
  }

  showTrayNotification() {
    if (Notification.isSupported()) {
      const notification = new Notification({
        title: '犇犇AI助手',
        body: '应用已最小化到系统托盘，双击托盘图标可重新打开',
        icon: join(__dirname, '../public/assets/logo.ico')
      })
      notification.show()
    }
  }

  checkLoginStatus() {
    // 检查是否有存储的登录信息
    const store = new Store()
    const userToken = store.get('userAuthToken')
    
    if (userToken) {
      console.log('🔑 发现存储的用户token，用户已登录')
      this.isLoggedIn = true
    } else {
      console.log('🔑 未发现用户token，用户未登录')
      this.isLoggedIn = false
    }
  }

  async initializeKnowledge() {
    console.log('🧠 初始化知识库...')
    try {
      const result = await initKnowledgeDatabase()
      if (result.success) {
        console.log('✅ 知识库初始化成功')
      } else {
        console.error('❌ 知识库初始化失败:', result.error)
      }
    } catch (error) {
      console.error('❌ 知识库初始化异常:', error)
    }
  }

  async initializeMCPServices() {
    console.log('🔧 初始化MCP服务...')
    try {
      await this.mcpManager.initialize()
      console.log('✅ MCP服务初始化完成')
    } catch (error) {
      console.error('❌ MCP服务初始化失败:', error)
    }
  }

  async initializeServices() {
    console.log('🔧 初始化其他服务...')
    
    try {
      // 初始化邮件服务
      this.emailService = new EmailService(this.mcpManager, this.mainWindow)
      await this.emailService.initialize()

      // 初始化Outlook日历服务
      this.outlookCalendarService = new OutlookCalendarService(this.mcpManager, this.mainWindow)
      await this.outlookCalendarService.initialize()

      this.servicesInitialized = true
      console.log('✅ 所有服务初始化完成')

      // 启动首次邮件检查
      if (this.emailService) {
        await this.emailService.startInitialEmailCheck()
      }

    } catch (error) {
      console.error('❌ 服务初始化失败:', error)
    }
  }

  setupIPC() {
    console.log('🔧 设置IPC处理程序...')

    // MCP相关处理程序
    ipcMain.handle('get-mcp-clients', () => {
      return this.mcpManager.getClientsStatus()
    })

    ipcMain.handle('call-mcp-tool', async (event, toolName, args, clientName) => {
      return await this.mcpManager.callRealMCPTool(toolName, args, clientName)
    })

    // 知识库相关处理程序
    ipcMain.handle('search-knowledge', async (event, query, limit, fileType) => {
      return await searchKnowledge(query, limit, fileType)
    })

    ipcMain.handle('get-knowledge-stats', async () => {
      return await getKnowledgeStats()
    })

    ipcMain.handle('index-knowledge-document', async (event, filePath) => {
      return await indexKnowledgeDocument(filePath)
    })

    ipcMain.handle('clear-knowledge-base', async () => {
      return await clearKnowledgeBase()
    })

    ipcMain.handle('rebuild-knowledge-base', async () => {
      return await rebuildKnowledgeBase()
    })

    ipcMain.handle('get-knowledge-document-files', async (event, dirPath) => {
      return await getKnowledgeDocumentFiles(dirPath)
    })

    ipcMain.handle('update-knowledge-config', (event, newConfig) => {
      updateKnowledgeConfig(newConfig)
      return { success: true }
    })

    // 文件系统相关处理程序
    ipcMain.handle('select-directory', async () => {
      const result = await dialog.showOpenDialog(this.mainWindow, {
        properties: ['openDirectory']
      })

      if (!result.canceled && result.filePaths.length > 0) {
        return { success: true, path: result.filePaths[0] }
      }

      return { success: false, error: '用户取消选择' }
    })

    ipcMain.handle('select-files', async (event, options = {}) => {
      const dialogOptions = {
        properties: ['openFile'],
        filters: options.filters || [
          { name: '所有文件', extensions: ['*'] }
        ]
      }

      if (options.multiple) {
        dialogOptions.properties.push('multiSelections')
      }

      const result = await dialog.showOpenDialog(this.mainWindow, dialogOptions)

      if (!result.canceled && result.filePaths.length > 0) {
        return { success: true, paths: result.filePaths }
      }

      return { success: false, error: '用户取消选择' }
    })

    // 系统相关处理程序
    ipcMain.handle('open-external', async (event, url) => {
      try {
        await shell.openExternal(url)
        return { success: true }
      } catch (error) {
        return { success: false, error: error.message }
      }
    })

    ipcMain.handle('show-item-in-folder', (event, fullPath) => {
      shell.showItemInFolder(fullPath)
      return { success: true }
    })

    // 邮件配置相关处理程序
    ipcMain.handle('get-email-config', () => {
      const store = new Store()
      const config = store.get('emailConfig', {})
      // 不返回密码，只返回其他配置信息
      return {
        user: config.user || '',
        server: config.server || 'imap.qq.com',
        port: config.port || 993,
        hasPassword: !!config.pass
      }
    })

    ipcMain.handle('save-email-config', (event, config) => {
      try {
        const store = new Store()
        store.set('emailConfig', config)
        console.log('📧 邮件配置已保存')
        return { success: true }
      } catch (error) {
        console.error('❌ 保存邮件配置失败:', error)
        return { success: false, error: error.message }
      }
    })

    ipcMain.handle('test-email-config', async (event, config) => {
      try {
        // 这里可以添加邮件配置测试逻辑
        console.log('📧 测试邮件配置:', { user: config.user, server: config.server, port: config.port })

        // 模拟测试成功
        return {
          success: true,
          message: '邮件配置测试成功（模拟）'
        }
      } catch (error) {
        console.error('❌ 邮件配置测试失败:', error)
        return { success: false, error: error.message }
      }
    })

    ipcMain.handle('delete-email-config', () => {
      try {
        const store = new Store()
        store.delete('emailConfig')
        console.log('📧 邮件配置已删除')
        return { success: true }
      } catch (error) {
        console.error('❌ 删除邮件配置失败:', error)
        return { success: false, error: error.message }
      }
    })

    // 应用状态相关处理程序
    ipcMain.handle('get-app-info', () => {
      return {
        version: app.getVersion(),
        name: app.getName(),
        isLoggedIn: this.isLoggedIn,
        servicesInitialized: this.servicesInitialized
      }
    })

    ipcMain.handle('minimize-to-tray', () => {
      this.mainWindow.hide()
      return { success: true }
    })

    ipcMain.handle('quit-app', () => {
      app.isQuiting = true
      app.quit()
    })

    console.log('✅ IPC处理程序设置完成')
  }

  async cleanup() {
    console.log('🧹 开始清理应用管理器...')

    try {
      // 清理邮件服务
      if (this.emailService) {
        this.emailService.cleanup()
        this.emailService = null
      }

      // 清理Outlook日历服务
      if (this.outlookCalendarService) {
        this.outlookCalendarService.cleanup()
        this.outlookCalendarService = null
      }

      // 清理MCP管理器
      if (this.mcpManager) {
        await this.mcpManager.cleanup()
        this.mcpManager = null
      }

      // 清理托盘
      if (this.tray) {
        this.tray.destroy()
        this.tray = null
      }

      // 清理全局引用
      global.mainWindow = null

      console.log('✅ 应用管理器清理完成')
    } catch (error) {
      console.error('❌ 应用管理器清理失败:', error)
    }
  }

  // 获取应用状态
  getStatus() {
    return {
      isLoggedIn: this.isLoggedIn,
      servicesInitialized: this.servicesInitialized,
      hasMainWindow: !!this.mainWindow,
      hasTray: !!this.tray,
      mcpClientsCount: this.mcpManager ? this.mcpManager.clients.size : 0
    }
  }
}

module.exports = AppManager
