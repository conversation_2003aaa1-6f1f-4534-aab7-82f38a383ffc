const { app, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ray, ipc<PERSON>ain, Notification } = require('electron')
const { join } = require('path')
const Store = require('electron-store')
const path = require('path')
const fs = require('fs')

// 尝试导入模块，如果失败则使用临时实现
let MCPClientManager = null
let EmailService = null
let OutlookCalendarService = null

try {
  // MCPClientManager = require('./mcp-manager')
  // EmailService = require('./email-service')
  // OutlookCalendarService = require('./calendar-service')
  console.log('📦 模块导入暂时跳过，使用基础功能')
} catch (error) {
  console.warn('⚠️ 某些模块导入失败，将使用基础功能:', error.message)
}

// 检查是否为开发环境
const isDev = process.env.NODE_ENV === 'development'

// 创建存储实例
const store = new Store()

// === 知识库配置和变量 ===

// 默认知识库配置
const DEFAULT_KNOWLEDGE_CONFIG = {
  database: {
    url: null, // 将在初始化时设置
    timeout: 30000
  },
  embedding: {
    baseURL: 'https://api.openai.com/v1',
    apiKey: 'your-api-key-here',
    model: 'text-embedding-ada-002',
    encoding_format: 'float',
    maxTokens: 8000
  },
  search: {
    defaultLimit: 10,
    similarityThreshold: 0.5,
    maxResultsPerDocument: 2
  },
  document: {
    supportedFormats: ['.txt', '.md', '.pdf', '.docx', '.doc'],
    maxSplitLength: 1000
  }
}

// 知识库全局变量
let libsqlClient = null
let openaiClient = null
let isKnowledgeInitialized = false
let KNOWLEDGE_CONFIG = { ...DEFAULT_KNOWLEDGE_CONFIG }

// 动态导入的模块
let OpenAI = null
let mammoth = null
let TurndownService = null
let createClient = null

// === 知识库核心函数 ===

/**
 * 初始化知识库依赖库
 */
async function initializeKnowledgeDependencies() {
  try {
    console.log('🔧 初始化知识库依赖...')

    // 动态导入依赖
    const libsql = await import('@libsql/client')
    createClient = libsql.createClient

    const openaiModule = await import('openai')
    OpenAI = openaiModule.default

    const mammothModule = await import('mammoth')
    mammoth = mammothModule

    const turndownModule = await import('turndown')
    TurndownService = turndownModule.default

    // 设置数据库路径（用户数据目录）
    const userDataPath = app.getPath('userData')
    const dbPath = path.join(userDataPath, 'knowledge.db')
    KNOWLEDGE_CONFIG.database.url = `file:${dbPath}`

    console.log('📂 知识库数据库路径:', dbPath)

    // 创建客户端
    libsqlClient = createClient({
      url: KNOWLEDGE_CONFIG.database.url,
      timeout: KNOWLEDGE_CONFIG.database.timeout
    })

    openaiClient = new OpenAI({
      baseURL: KNOWLEDGE_CONFIG.embedding.baseURL,
      apiKey: KNOWLEDGE_CONFIG.embedding.apiKey
    })

    console.log('✅ 知识库依赖初始化成功')
    return true
  } catch (error) {
    console.error('❌ 知识库依赖初始化失败:', error)
    return false
  }
}

/**
 * 初始化知识库数据库
 */
async function initKnowledgeDatabase() {
  try {
    if (!libsqlClient) {
      const success = await initializeKnowledgeDependencies()
      if (!success) {
        throw new Error('依赖初始化失败')
      }
    }

    console.log('🧠 开始初始化知识库数据库...')

    // 创建用户文件表
    await libsqlClient.execute(`
      CREATE TABLE IF NOT EXISTS user_file (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        file_name TEXT NOT NULL,
        file_path TEXT NOT NULL UNIQUE,
        file_type TEXT,
        file_size INTEGER,
        create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
        update_time DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `)

    // 创建文件嵌入表
    await libsqlClient.execute(`
      CREATE TABLE IF NOT EXISTS user_file_embd (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        file_id INTEGER NOT NULL,
        chunk_index INTEGER NOT NULL,
        content TEXT NOT NULL,
        embedding BLOB NOT NULL,
        create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (file_id) REFERENCES user_file (id) ON DELETE CASCADE
      )
    `)

    // 创建索引
    await libsqlClient.execute(`
      CREATE INDEX IF NOT EXISTS idx_user_file_embd_file_id ON user_file_embd(file_id)
    `)

    await libsqlClient.execute(`
      CREATE INDEX IF NOT EXISTS idx_user_file_create_time ON user_file(create_time)
    `)

    await libsqlClient.execute(`
      CREATE INDEX IF NOT EXISTS idx_user_file_embd_create_time ON user_file_embd(create_time)
    `)

    // 检查并添加缺失的列（数据库迁移）
    try {
      // 检查是否存在file_type列
      const tableInfo = await libsqlClient.execute(`PRAGMA table_info(user_file)`)
      const hasFileType = tableInfo.rows.some(row => row.name === 'file_type')

      if (!hasFileType) {
        console.log('🔧 添加缺失的file_type列...')
        await libsqlClient.execute(`ALTER TABLE user_file ADD COLUMN file_type TEXT`)

        // 为现有文件设置file_type
        await libsqlClient.execute(`
          UPDATE user_file
          SET file_type = CASE
            WHEN file_path LIKE '%.txt' THEN 'txt'
            WHEN file_path LIKE '%.md' THEN 'md'
            WHEN file_path LIKE '%.pdf' THEN 'pdf'
            WHEN file_path LIKE '%.docx' THEN 'docx'
            WHEN file_path LIKE '%.doc' THEN 'doc'
            ELSE 'unknown'
          END
          WHERE file_type IS NULL
        `)
        console.log('✅ file_type列添加完成')
      }

      // 检查是否存在file_size列
      const hasFileSize = tableInfo.rows.some(row => row.name === 'file_size')
      if (!hasFileSize) {
        console.log('🔧 添加缺失的file_size列...')
        await libsqlClient.execute(`ALTER TABLE user_file ADD COLUMN file_size INTEGER DEFAULT 0`)
        console.log('✅ file_size列添加完成')
      }
    } catch (migrationError) {
      console.warn('⚠️ 数据库迁移检查失败:', migrationError.message)
    }

    isKnowledgeInitialized = true
    console.log('✅ 知识库数据库初始化成功')
    return { success: true }
  } catch (error) {
    console.error('❌ 知识库数据库初始化失败:', error)
    return { success: false, error: error.message }
  }
}

/**
 * 获取知识库统计信息
 */
async function getKnowledgeStats() {
  try {
    if (!isKnowledgeInitialized) {
      console.log('🔧 知识库未初始化，开始初始化...')
      await initKnowledgeDatabase()
    }

    const fileCountResult = await libsqlClient.execute(
      'SELECT COUNT(*) as count FROM user_file'
    )

    const segmentCountResult = await libsqlClient.execute(
      'SELECT COUNT(*) as count FROM user_file_embd'
    )

    const totalFiles = fileCountResult.rows[0]?.count || 0
    const totalSegments = segmentCountResult.rows[0]?.count || 0

    console.log(`📊 知识库统计: ${totalFiles} 个文件, ${totalSegments} 个片段`)

    return {
      totalFiles: Number(totalFiles),
      totalSegments: Number(totalSegments)
    }
  } catch (error) {
    console.error('❌ 获取知识库统计失败:', error)
    return { totalFiles: 0, totalSegments: 0 }
  }
}

// 应用管理器类
class AppManager {
  constructor() {
    this.mainWindow = null
    this.floatingWindow = null
    this.tray = null
    this.mcpManager = null
    this.emailService = null
    this.calendarService = null
    this.isLoggedIn = false
    this.hasShownTrayNotification = false
  }

  async initialize() {
    console.log('🚀 开始初始化应用管理器...')

    // 设置控制台转发（暂时跳过）
    // setupConsoleForwarding()

    // 创建主窗口
    await this.createMainWindow()

    // 创建系统托盘
    this.createTray()

    // 设置应用菜单
    this.setupMenu()

    // 初始化服务
    await this.initializeServices()

    // 设置IPC处理程序
    this.setupIPC()

    console.log('✅ 应用管理器初始化完成')
  }

  async initializeServices() {
    console.log('🔧 开始初始化服务...')

    try {
      // 发送初始化状态消息
      this.sendStatusMessage('正在初始化服务...')

      // 暂时跳过服务初始化，直到模块依赖问题解决
      console.log('⚠️ 服务初始化暂时跳过（模块依赖问题）')

      // 模拟一些初始化时间，让用户看到加载过程
      await new Promise(resolve => setTimeout(resolve, 1000))

      // TODO: 重新启用服务初始化
      // this.mcpManager = new MCPClientManager()
      // await this.mcpManager.initialize()
      // this.emailService = new EmailService()
      // await this.emailService.initialize()
      // this.calendarService = new OutlookCalendarService()
      // await this.calendarService.initialize()
      // setupKnowledgeIPC()

      console.log('✅ 基础服务初始化完成')

      // 发送应用初始化完成消息
      this.sendStatusMessage('应用初始化完成')

    } catch (error) {
      console.error('❌ 服务初始化失败:', error)
      // 即使服务初始化失败，也不要阻止应用启动
      this.sendStatusMessage('应用初始化完成')
    }
  }

  // 发送状态消息到渲染进程
  sendStatusMessage(message) {
    console.log('📤 发送状态消息:', message)
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.webContents.send('status-message', message)
    }
  }

  async createMainWindow() {
    console.log('🪟 创建主窗口...')

    this.mainWindow = new BrowserWindow({
      width: 1200,
      height: 800,
      minWidth: 800,
      minHeight: 600,
      webPreferences: {
        preload: isDev
          ? join(process.cwd(), 'src/preload/preload.js')
          : join(__dirname, 'preload.js'),
        nodeIntegration: false,
        contextIsolation: true,
        webSecurity: false, // 允许跨域请求以支持各种AI服务API
        allowRunningInsecureContent: true, // 允许不安全内容以支持各种API
        experimentalFeatures: true, // 启用实验性功能支持WebRTC等
        additionalArguments: ['--enable-features=WebRTC-H264WithOpenH264FFmpeg'] // 支持WebRTC
      },
      icon: isDev ? join(process.cwd(), 'public/assets/logo.ico') : join(__dirname, '../public/assets/logo.ico'),
      show: false,
      titleBarStyle: 'default'
    })

    // 设置全局引用
    global.mainWindow = this.mainWindow

    // 加载应用
    if (isDev) {
      this.mainWindow.loadURL('http://localhost:6913')
      // 开发环境自动打开开发者工具
      this.mainWindow.webContents.openDevTools()
    } else {
      this.mainWindow.loadFile(join(__dirname, '../build/index.html'))
    }

    // 窗口准备显示时显示窗口
    this.mainWindow.once('ready-to-show', () => {
      console.log('🪟 主窗口准备就绪，显示窗口')
      this.mainWindow.show()
      this.checkLoginStatus()
    })

    // 窗口关闭时隐藏到托盘
    this.mainWindow.on('close', (event) => {
      if (!app.isQuiting) {
        event.preventDefault()
        this.mainWindow.hide()

        if (!this.hasShownTrayNotification) {
          this.showTrayNotification()
          this.hasShownTrayNotification = true
        }
      }
    })

    console.log('✅ 主窗口创建完成')
  }

  createTray() {
    console.log('🔔 创建系统托盘...')

    const trayIconPath = isDev ? join(process.cwd(), 'public/assets/logo.ico') : join(__dirname, '../public/assets/logo.ico')
    this.tray = new Tray(trayIconPath)

    const contextMenu = Menu.buildFromTemplate([
      {
        label: '显示主窗口',
        click: () => {
          this.mainWindow.show()
          this.mainWindow.focus()
        }
      },
      {
        label: '退出',
        click: () => {
          app.isQuiting = true
          app.quit()
        }
      }
    ])

    this.tray.setToolTip('犇犇AI助手')
    this.tray.setContextMenu(contextMenu)

    this.tray.on('double-click', () => {
      this.mainWindow.show()
      this.mainWindow.focus()
    })

    console.log('✅ 系统托盘创建完成')
  }

  setupMenu() {
    if (process.platform === 'darwin') {
      const template = [
        {
          label: app.getName(),
          submenu: [
            { role: 'about' },
            { type: 'separator' },
            { role: 'quit' }
          ]
        }
      ]
      const menu = Menu.buildFromTemplate(template)
      Menu.setApplicationMenu(menu)
    } else {
      Menu.setApplicationMenu(null)
    }
  }

  setupIPC() {
    console.log('🔧 设置IPC处理程序...')

    // 基本应用信息
    ipcMain.handle('get-app-info', () => {
      return {
        version: app.getVersion(),
        name: app.getName(),
        isLoggedIn: this.isLoggedIn
      }
    })

    // 窗口控制
    ipcMain.handle('minimize-to-tray', () => {
      this.mainWindow.hide()
      return { success: true }
    })

    ipcMain.handle('quit-app', () => {
      app.isQuiting = true
      app.quit()
    })

    // MCP相关IPC
    if (this.mcpManager) {
      ipcMain.handle('call-mcp-tool', async (_, toolName, args) => {
        try {
          return await this.mcpManager.callTool(toolName, args)
        } catch (error) {
          console.error('MCP工具调用失败:', error)
          return { error: error.message }
        }
      })

      ipcMain.handle('get-mcp-tools', async () => {
        try {
          return await this.mcpManager.getAvailableTools()
        } catch (error) {
          console.error('获取MCP工具失败:', error)
          return { error: error.message }
        }
      })
    }

    // 邮件服务IPC
    if (this.emailService) {
      ipcMain.handle('check-emails', async () => {
        try {
          return await this.emailService.checkEmails()
        } catch (error) {
          console.error('检查邮件失败:', error)
          return { error: error.message }
        }
      })
    }

    // 日历服务IPC
    if (this.calendarService) {
      ipcMain.handle('get-calendar-events', async (_, startDate, endDate) => {
        try {
          return await this.calendarService.getEvents(startDate, endDate)
        } catch (error) {
          console.error('获取日历事件失败:', error)
          return { error: error.message }
        }
      })
    }

    // 状态消息广播IPC
    ipcMain.handle('send-status-message', async (_, message) => {
      try {
        console.log('🔄 收到状态消息广播请求:', message)
        this.sendStatusMessage(message)
        return { success: true }
      } catch (error) {
        console.error('❌ 发送状态消息失败:', error)
        return { success: false, error: error.message }
      }
    })

    // 开发者工具切换IPC
    ipcMain.on('toggle-dev-tools', (_, enabled) => {
      console.log('🔧 切换开发者工具:', enabled)

      // 打开/关闭主窗口的开发者工具
      if (this.mainWindow) {
        if (enabled) {
          this.mainWindow.webContents.openDevTools()
        } else {
          this.mainWindow.webContents.closeDevTools()
        }
      }
    })

    // === 知识库相关的IPC处理器 ===

    // 初始化知识库
    ipcMain.handle('knowledge-init', async () => {
      try {
        console.log('🧠 收到知识库初始化请求')
        const result = await initKnowledgeDatabase()
        console.log('🧠 知识库初始化结果:', result)
        return result
      } catch (error) {
        console.error('🧠 知识库初始化失败:', error)
        return { success: false, error: error.message }
      }
    })

    // 获取知识库统计
    ipcMain.handle('knowledge-stats', async () => {
      try {
        console.log('🧠 收到知识库统计请求')
        const stats = await getKnowledgeStats()
        console.log('🧠 知识库统计结果:', stats)
        return stats
      } catch (error) {
        console.error('🧠 获取知识库统计失败:', error)
        return { totalFiles: 0, totalSegments: 0 }
      }
    })

    // 列出知识库文件
    ipcMain.handle('knowledge-list-files', async (_, fileType, fileName, pageSize = 10, pageNum = 1) => {
      try {
        console.log(`🧠 收到列出文件请求: fileType=${fileType}, fileName=${fileName}, pageSize=${pageSize}, pageNum=${pageNum}`)

        if (!isKnowledgeInitialized) {
          console.log('🧠 知识库未初始化，开始初始化...')
          await initKnowledgeDatabase()
        }

        // 构建查询条件
        let whereConditions = []
        let args = []

        if (fileType !== null && fileType !== undefined) {
          whereConditions.push('file_type = ?')
          args.push(fileType)
        }

        if (fileName && fileName.trim()) {
          whereConditions.push('file_name LIKE ?')
          args.push(`%${fileName.trim()}%`)
        }

        const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : ''

        // 获取总数
        const countSql = `SELECT COUNT(*) as total FROM user_file ${whereClause}`
        const countResult = await libsqlClient.execute({ sql: countSql, args })
        const total = Number(countResult.rows[0]?.total || 0)

        // 获取分页数据
        const offset = (pageNum - 1) * pageSize
        const dataSql = `
          SELECT id, file_name, file_path, file_type, file_size, create_time, update_time
          FROM user_file ${whereClause}
          ORDER BY create_time DESC
          LIMIT ? OFFSET ?
        `
        const dataResult = await libsqlClient.execute({
          sql: dataSql,
          args: [...args, pageSize, offset]
        })

        const files = dataResult.rows.map(row => ({
          id: row.id,
          fileName: row.file_name,
          filePath: row.file_path,
          fileType: row.file_type,
          fileSize: row.file_size,
          createTime: row.create_time,
          updateTime: row.update_time
        }))

        console.log(`🧠 找到 ${files.length} 个文件，总计 ${total} 个`)
        return { files, total, pageSize, pageNum }
      } catch (error) {
        console.error('🧠 列出文件失败:', error)
        return { files: [], total: 0, pageSize, pageNum }
      }
    })

    // 获取用户数据路径
    ipcMain.handle('get-path', async (_, pathName) => {
      console.log('🧠 收到获取路径请求:', pathName)
      try {
        return app.getPath(pathName)
      } catch (error) {
        console.error('❌ 获取路径失败:', error)
        return null
      }
    })

    // 获取应用版本
    ipcMain.handle('get-app-version', async () => {
      console.log('📱 收到获取应用版本请求')
      return app.getVersion()
    })

    // 设置登录状态（临时实现）
    ipcMain.handle('set-login-status', async (_, status) => {
      console.log('🔑 收到设置登录状态请求:', status)
      return { success: true }
    })

    // === 添加缺失的基础IPC处理器 ===

    // 登录相关
    ipcMain.handle('login', async (_, credentials) => {
      console.log('🔑 收到登录请求:', credentials.username)
      if (credentials.username && credentials.password) {
        this.isLoggedIn = true
        return { success: true, message: '登录成功', user: { username: credentials.username } }
      }
      return { success: false, message: '用户名或密码错误' }
    })

    ipcMain.handle('logout', async () => {
      console.log('🚪 收到注销请求')
      this.isLoggedIn = false
      return { success: true }
    })

    ipcMain.handle('get-login-status', () => {
      console.log('🔍 收到获取登录状态请求')
      return this.isLoggedIn || false
    })

    // 窗口控制
    ipcMain.on('show-main-window', (_, page) => {
      console.log('🪟 显示主窗口:', page)
      if (this.mainWindow) {
        this.mainWindow.show()
        this.mainWindow.focus()
        if (page) {
          this.mainWindow.webContents.send('navigate-to', page)
        }
      } else {
        this.createMainWindow()
      }
    })

    ipcMain.on('hide-main-window', () => {
      console.log('🪟 隐藏主窗口')
      if (this.mainWindow) {
        this.mainWindow.hide()
      }
    })

    ipcMain.on('close-app', () => {
      console.log('🚪 关闭应用')
      app.quit()
    })

    // 打开URL
    ipcMain.handle('open-url', async (_, url) => {
      try {
        console.log('🔗 打开URL:', url)
        await shell.openExternal(url)
        return { success: true }
      } catch (error) {
        console.error('🔗 打开URL失败:', error)
        return { success: false, error: error.message }
      }
    })

    // 语音播放
    ipcMain.handle('speak-welcome-message', async (_, options) => {
      try {
        console.log('🔊 收到欢迎语音播放请求:', options)
        // 这里可以添加实际的语音播放逻辑
        return { success: true }
      } catch (error) {
        console.error('🔊 播放欢迎语音失败:', error)
        return { success: false, error: error.message }
      }
    })

    console.log('✅ IPC处理程序设置完成')
  }

  showTrayNotification() {
    if (Notification.isSupported()) {
      const notification = new Notification({
        title: '犇犇AI助手',
        body: '应用已最小化到系统托盘，双击托盘图标可重新打开',
        icon: isDev ? join(process.cwd(), 'public/assets/logo.ico') : join(__dirname, '../public/assets/logo.ico')
      })
      notification.show()
    }
  }

  checkLoginStatus() {
    const store = new Store()
    const userToken = store.get('userAuthToken')

    if (userToken) {
      console.log('🔑 发现存储的用户token，用户已登录')
      this.isLoggedIn = true
    } else {
      console.log('🔑 未发现用户token，用户未登录')
      this.isLoggedIn = false
    }
  }

  async cleanup() {
    console.log('🧹 开始清理应用管理器...')

    // 清理服务
    if (this.mcpManager) {
      await this.mcpManager.cleanup()
      this.mcpManager = null
    }

    if (this.emailService) {
      if (typeof this.emailService.cleanup === 'function') {
        this.emailService.cleanup()
      }
      this.emailService = null
    }

    if (this.calendarService) {
      if (typeof this.calendarService.cleanup === 'function') {
        this.calendarService.cleanup()
      }
      this.calendarService = null
    }

    // 清理托盘
    if (this.tray) {
      this.tray.destroy()
      this.tray = null
    }

    global.mainWindow = null
    console.log('✅ 应用管理器清理完成')
  }
}

// 创建应用管理器实例
let appManager = null

// 应用事件处理
app.whenReady().then(async () => {
  console.log('🚀 Electron应用已准备就绪')
  
  try {
    // 创建并初始化应用管理器
    appManager = new AppManager()
    await appManager.initialize()
    
    console.log('✅ 应用初始化完成')
  } catch (error) {
    console.error('❌ 应用初始化失败:', error)
    // 即使初始化失败，也不要退出应用，让用户能够看到错误信息
  }
})

// 当所有窗口都关闭时
app.on('window-all-closed', () => {
  // 在macOS上，应用通常会保持活跃状态，即使没有打开的窗口
  // 除非用户明确使用Cmd + Q退出应用
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

// 当应用被激活时（macOS）
app.on('activate', async () => {
  // 在macOS上，当点击dock图标且没有其他窗口打开时，
  // 通常会重新创建一个窗口
  if (appManager && appManager.mainWindow === null) {
    await appManager.createMainWindow()
  }
})

// 应用即将退出时的清理工作
app.on('before-quit', async (event) => {
  console.log('🧹 应用即将退出，开始清理...')
  
  if (appManager) {
    // 防止默认的退出行为，先进行清理
    event.preventDefault()
    
    try {
      await appManager.cleanup()
      console.log('✅ 应用清理完成')
    } catch (error) {
      console.error('❌ 应用清理失败:', error)
    } finally {
      // 清理完成后真正退出应用
      app.exit(0)
    }
  }
})

// 处理未捕获的异常
process.on('uncaughtException', (error) => {
  console.error('❌ 未捕获的异常:', error)
  console.error('❌ 错误堆栈:', error.stack)
  
  // 在生产环境中，可能需要重启应用或显示错误对话框
  if (!isDev) {
    // 可以在这里添加错误报告逻辑
    console.error('🚨 生产环境中发生未捕获异常，应用可能不稳定')
  }
})

// 处理未处理的Promise拒绝
process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ 未处理的Promise拒绝:', reason)
  console.error('❌ Promise:', promise)
  
  if (!isDev) {
    console.error('🚨 生产环境中发生未处理的Promise拒绝')
  }
})

// 设置应用的安全策略
app.on('web-contents-created', (_, contents) => {
  // 防止新窗口的创建
  contents.on('new-window', (event, navigationUrl) => {
    event.preventDefault()
    console.warn('🚨 阻止了新窗口的创建:', navigationUrl)
  })
  
  // 防止导航到外部URL
  contents.on('will-navigate', (event, navigationUrl) => {
    const parsedUrl = new URL(navigationUrl)
    
    // 只允许本地开发服务器和本地文件
    if (parsedUrl.origin !== 'http://localhost:6913' &&
        parsedUrl.protocol !== 'file:') {
      event.preventDefault()
      console.warn('🚨 阻止了导航到外部URL:', navigationUrl)
    }
  })
})

// 导出应用管理器实例（用于测试或调试）
module.exports = {
  getAppManager: () => appManager,
  isDev
}
