const { app, BrowserWindow, ipcMain, screen, Tray, Menu, nativeImage, shell, dialog } = require('electron')
const { join, basename } = require('path')
const fs = require('fs')
const Store = require('electron-store')
let { spawn } = require('child_process')
const os = require('os')
const path = require('path')
const axios = require('axios')

// API客户端配置和函数直接集成到main.js中
// 新的 Sherpa-ONNX 集成方式：使用 Web Audio API 和 WASM，无需主进程处理

// === 主进程API客户端配置 ===
const MAIN_API_CONFIG = {
  PROD_API_BASE_URL: 'http://*************:9603/prod-api',
  REQUEST_CONFIG: {
    TIMEOUT: 30000,
    SEARCH_TIMEOUT: 120000
  }
}

/**
 * 创建主进程API客户端
 * @param {string} baseURL - API基础地址
 * @param {string} userToken - 用户token
 * @returns {Object} axios实例
 */
function createMainApiClient(baseURL = null, userToken = '') {
  // 使用配置中心的API地址
  const apiBaseUrl = baseURL || `${MAIN_API_CONFIG.PROD_API_BASE_URL}/api`
  const headers = {
    'Content-Type': 'application/json'
  }

  // 如果有token则添加Authorization头
  if (userToken) {
    headers['Authorization'] = `Bearer ${userToken}`
  }

  const client = axios.create({
    baseURL: apiBaseUrl,
    headers,
    timeout: MAIN_API_CONFIG.REQUEST_CONFIG.TIMEOUT
  })

  // 请求拦截器
  client.interceptors.request.use(
    (config) => {
      console.log('🌐 主进程API请求:', {
        method: config.method?.toUpperCase(),
        url: config.url,
        baseURL: config.baseURL
      })
      return config
    },
    (error) => {
      console.error('🌐 主进程API请求拦截器错误:', error)
      return Promise.reject(error)
    }
  )

  // 响应拦截器
  client.interceptors.response.use(
    (response) => {
      console.log('🌐 主进程API响应:', {
        status: response.status,
        url: response.config.url,
        dataKeys: response.data ? Object.keys(response.data) : []
      })
      return response
    },
    (error) => {
      console.error('🌐 主进程API响应错误:', {
        status: error.response?.status,
        url: error.config?.url,
        message: error.message
      })
      return Promise.reject(error)
    }
  )

  return client
}

/**
 * 获取当前用户token
 */
async function getCurrentUserToken() {
  try {
    const store = new Store()
    const token = store.get('userAuthToken')
    return token
  } catch (error) {
    console.error('获取用户token失败:', error)
    return null
  }
}

// 尝试导入模块，如果失败则使用临时实现
let MCPClientManager = null
let EmailService = null
let OutlookCalendarService = null

try {
  // MCPClientManager = require('./mcp-manager')
  // EmailService = require('./email-service')
  // OutlookCalendarService = require('./calendar-service')
  console.log('📦 模块导入暂时跳过，使用基础功能')
} catch (error) {
  console.warn('⚠️ 某些模块导入失败，将使用基础功能:', error.message)
}

// 检查是否为开发环境
const isDev = process.env.NODE_ENV === 'development'

// 创建存储实例
const store = new Store()

// === 知识库配置和变量 ===

// 默认知识库配置（主进程使用）
const DEFAULT_KNOWLEDGE_CONFIG = {
  database: {
    url: null, // 将在初始化时设置
    timeout: 30000
  },
  embedding: {
    // 使用代理接口，不需要直接配置外部API
    model: 'BAAI/bge-m3',
    encoding_format: 'float',
    maxTokens: 8000 // API支持的最大token数
  },
  document: {
    minSplitLength: 500,     // 最小分割长度 (降低到500字符)
    maxSplitLength: 1000,    // 最大分割长度 (降低到1000字符，避免API限制)
    supportedFormats: ['.txt', '.md', '.docx', '.doc']
  },
  search: {
    defaultLimit: 4,          // 降低默认返回数量，确保质量
    similarityThreshold: 0.4,  // 提高相似度阈值到40%，确保相关性
    minSimilarityThreshold: 0.4, // 最低相似度阈值，低于此值直接排除
    maxResultsPerDocument: 2  // 每个文档最多返回2个片段
  }
}

// 知识库全局变量
let libsqlClient = null
let openaiClient = null
let isKnowledgeInitialized = false
let KNOWLEDGE_CONFIG = { ...DEFAULT_KNOWLEDGE_CONFIG }

// 动态导入的模块
let OpenAI = null
let mammoth = null
let TurndownService = null
let createClient = null

// === 知识库配置管理函数 ===

// 从渲染进程获取最新配置的函数
function getKnowledgeConfig() {
  // 优先使用渲染进程的配置，如果没有则使用默认配置
  return global.sharedKnowledgeConfig || DEFAULT_KNOWLEDGE_CONFIG
}

// 更新知识库配置
function updateKnowledgeConfig(newConfig) {
  if (global.sharedKnowledgeConfig) {
    global.sharedKnowledgeConfig = { ...global.sharedKnowledgeConfig, ...newConfig }
  } else {
    global.sharedKnowledgeConfig = { ...DEFAULT_KNOWLEDGE_CONFIG, ...newConfig }
  }

  // 更新当前使用的配置
  KNOWLEDGE_CONFIG = getKnowledgeConfig()

  console.log('🧠 知识库配置已更新:', KNOWLEDGE_CONFIG)
}

// === 知识库核心函数 ===

/**
 * 初始化知识库依赖库
 */
async function initializeKnowledgeDependencies() {
  try {
    console.log('🔧 初始化知识库依赖...')

    // 动态导入依赖
    const libsql = await import('@libsql/client')
    createClient = libsql.createClient

    // 文档处理依赖（用于文档索引功能）
    const mammothModule = await import('mammoth')
    mammoth = mammothModule

    const turndownModule = await import('turndown')
    TurndownService = turndownModule.default

    // 设置数据库路径（用户数据目录）
    const userDataPath = app.getPath('userData')
    const dbPath = path.join(userDataPath, 'knowledge.db')
    KNOWLEDGE_CONFIG.database.url = `file:${dbPath}`

    console.log('📂 知识库数据库路径:', dbPath)

    // 创建数据库客户端
    libsqlClient = createClient({
      url: KNOWLEDGE_CONFIG.database.url,
      timeout: KNOWLEDGE_CONFIG.database.timeout
    })

    // 不再需要直接创建OpenAI客户端，使用代理接口

    console.log('✅ 知识库依赖初始化成功')
    return true
  } catch (error) {
    console.error('❌ 知识库依赖初始化失败:', error)
    return false
  }
}

/**
 * 初始化知识库数据库
 */
async function initKnowledgeDatabase() {
  try {
    if (!libsqlClient) {
      const success = await initializeKnowledgeDependencies()
      if (!success) {
        throw new Error('依赖初始化失败')
      }
    }

    console.log('🧠 开始初始化知识库数据库...')

    // 创建用户文件表
    await libsqlClient.execute(`
      CREATE TABLE IF NOT EXISTS user_file (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        file_name TEXT NOT NULL,
        file_path TEXT NOT NULL UNIQUE,
        file_type TEXT,
        file_size INTEGER,
        create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
        update_time DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `)

    // 创建文件嵌入表
    await libsqlClient.execute(`
      CREATE TABLE IF NOT EXISTS user_file_embd (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        file_id INTEGER NOT NULL,
        chunk_index INTEGER NOT NULL,
        content TEXT NOT NULL,
        embedding BLOB NOT NULL,
        create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (file_id) REFERENCES user_file (id) ON DELETE CASCADE
      )
    `)

    // 创建索引
    await libsqlClient.execute(`
      CREATE INDEX IF NOT EXISTS idx_user_file_embd_file_id ON user_file_embd(file_id)
    `)

    await libsqlClient.execute(`
      CREATE INDEX IF NOT EXISTS idx_user_file_create_time ON user_file(create_time)
    `)

    await libsqlClient.execute(`
      CREATE INDEX IF NOT EXISTS idx_user_file_embd_create_time ON user_file_embd(create_time)
    `)

    // 检查并添加缺失的列（数据库迁移）
    try {
      // 检查是否存在file_type列
      const tableInfo = await libsqlClient.execute(`PRAGMA table_info(user_file)`)
      const hasFileType = tableInfo.rows.some(row => row.name === 'file_type')

      if (!hasFileType) {
        console.log('🔧 添加缺失的file_type列...')
        await libsqlClient.execute(`ALTER TABLE user_file ADD COLUMN file_type TEXT`)

        // 为现有文件设置file_type
        await libsqlClient.execute(`
          UPDATE user_file
          SET file_type = CASE
            WHEN file_path LIKE '%.txt' THEN 'txt'
            WHEN file_path LIKE '%.md' THEN 'md'
            WHEN file_path LIKE '%.pdf' THEN 'pdf'
            WHEN file_path LIKE '%.docx' THEN 'docx'
            WHEN file_path LIKE '%.doc' THEN 'doc'
            ELSE 'unknown'
          END
          WHERE file_type IS NULL
        `)
        console.log('✅ file_type列添加完成')
      }

      // 检查是否存在file_size列
      const hasFileSize = tableInfo.rows.some(row => row.name === 'file_size')
      if (!hasFileSize) {
        console.log('🔧 添加缺失的file_size列...')
        await libsqlClient.execute(`ALTER TABLE user_file ADD COLUMN file_size INTEGER DEFAULT 0`)
        console.log('✅ file_size列添加完成')
      }

      // 检查是否存在update_time列
      const hasUpdateTime = tableInfo.rows.some(row => row.name === 'update_time')
      if (!hasUpdateTime) {
        console.log('🔧 添加缺失的update_time列...')
        // SQLite不支持带有CURRENT_TIMESTAMP的ALTER TABLE，所以先添加NULL列
        await libsqlClient.execute(`ALTER TABLE user_file ADD COLUMN update_time DATETIME`)

        // 为现有记录设置update_time为create_time
        await libsqlClient.execute(`
          UPDATE user_file
          SET update_time = create_time
          WHERE update_time IS NULL
        `)
        console.log('✅ update_time列添加完成')
      }
    } catch (migrationError) {
      console.warn('⚠️ 数据库迁移检查失败:', migrationError.message)
    }

    isKnowledgeInitialized = true
    console.log('✅ 知识库数据库初始化成功')
    return { success: true }
  } catch (error) {
    console.error('❌ 知识库数据库初始化失败:', error)
    return { success: false, error: error.message }
  }
}

/**
 * 获取文本的向量表示（使用代理）
 */
async function getKnowledgeEmbedding(text) {
  try {
    // 检查文本长度，如果过长则截断
    let processedText = text
    if (text.length > KNOWLEDGE_CONFIG.embedding.maxTokens) {
      console.log(`⚠️ 文本过长 (${text.length} 字符)，截断到 ${KNOWLEDGE_CONFIG.embedding.maxTokens} 字符`)
      processedText = text.substring(0, KNOWLEDGE_CONFIG.embedding.maxTokens)
    }

    console.log(`🔗 正在通过代理向量化文本，长度: ${processedText.length} 字符`)

    // 获取用户token并创建API客户端
    const userToken = await getCurrentUserToken()
    console.log('🔑 向量化请求使用的token:', userToken ? userToken.substring(0, 20) + '...' : '无token')
    const client = createMainApiClient(null, userToken)

    const requestData = {
      model: KNOWLEDGE_CONFIG.embedding.model,
      input: processedText,
      encoding_format: KNOWLEDGE_CONFIG.embedding.encoding_format
    }

    // 使用正确的代理端点 /api/embeddings
    const response = await client.post('/api/embeddings', requestData)

    // 打印完整的响应信息用于调试
    console.log('🔍 完整响应对象:', {
      status: response.status,
      statusText: response.statusText,
      headers: response.headers,
      data: response.data
    })

    // 处理嵌套的响应结构
    const responseData = response.data.data || response.data
    console.log('🔍 响应数据结构:', {
      hasResponseData: !!responseData,
      responseDataType: typeof responseData,
      isArray: Array.isArray(responseData),
      hasData: responseData && responseData.data,
      dataLength: responseData && responseData.data ? responseData.data.length : 0
    })

    if (responseData && responseData.data && Array.isArray(responseData.data) && responseData.data.length > 0) {
      const embedding = responseData.data[0].embedding
      if (embedding && Array.isArray(embedding)) {
        console.log(`✅ 向量化成功，维度: ${embedding.length}`)
        return embedding
      }
    }

    // 如果上面的结构不匹配，尝试其他可能的结构
    if (Array.isArray(responseData) && responseData.length > 0 && responseData[0].embedding) {
      const embedding = responseData[0].embedding
      console.log(`✅ 向量化成功（备用结构），维度: ${embedding.length}`)
      return embedding
    }

    throw new Error('响应格式不正确或缺少embedding数据')
  } catch (error) {
    console.error('❌ 向量化失败:', error)
    throw error
  }
}

/**
 * 使用重排序模型并过滤相似度低的片段（使用代理）
 */
async function rerank(similarChunks, queryText) {
  try {
    const documents = similarChunks.map(chunk => chunk.content);

    console.log('🔄 正在通过代理重排序文档片段:', {
      documentsCount: documents.length,
      queryLength: queryText.length
    });

    // 获取用户token并创建API客户端
    const userToken = await getCurrentUserToken()
    console.log('🔑 重排序请求使用的token:', userToken ? userToken.substring(0, 20) + '...' : '无token')
    const client = createMainApiClient(null, userToken)

    const requestData = {
      query: queryText,
      documents: documents,
      model: "BAAI/bge-reranker-v2-m3"
    };

    // 使用正确的代理端点 /api/rerank
    const response = await client.post('/api/rerank', requestData);

    // 处理嵌套的响应结构
    const responseData = response.data.data || response.data;
    console.log('🔍 重排序响应数据结构:', {
      hasResponseData: !!responseData,
      responseDataType: typeof responseData,
      hasResults: responseData && responseData.results,
      resultsLength: responseData && responseData.results ? responseData.results.length : 0
    })

    if (responseData && responseData.results) {
      console.log(`✅ 代理重排序成功，返回 ${responseData.results.length} 个结果`);
      return getTopChunks(responseData, similarChunks);
    }
  } catch (err) {
    console.error('❌ 代理重排序失败:', err);
  }
  return similarChunks;
}

/**
 * 动态阈值筛选优质片段
 */
async function getTopChunks(response, chunks, topN = 4, minScore = 0.3) {
  // 提取并排序结果（按相关性分数降序）
  const sortedResults = response.results
    .slice() // 创建副本避免修改原数组
    .sort((a, b) => b.relevance_score - a.relevance_score);

  // 计算统计指标
  const scores = sortedResults.map(res => res.relevance_score);
  const mean = scores.reduce((sum, val) => sum + val, 0) / scores.length;
  const stdDev = Math.sqrt(
    scores.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / scores.length
  );

  // 改进的动态阈值计算
  // 使用更宽松的阈值：均值减去0.5个标准差，或者直接使用前N个结果
  const dynamicThreshold = Math.max(minScore, mean - 0.5 * stdDev);
  const finalThreshold = Math.min(dynamicThreshold, 0.05); // 设置上限，避免过于严格

  console.log(`📊 重排序统计: 均值=${mean.toFixed(3)}, 标准差=${stdDev.toFixed(3)}, 动态阈值=${finalThreshold.toFixed(3)}`);
  console.log(`📊 相关性分数分布:`, scores.map(s => s.toFixed(3)));

  // 如果动态阈值过滤掉太多结果，则直接返回前N个
  const filteredResults = sortedResults.filter(res => res.relevance_score >= finalThreshold);

  if (filteredResults.length === 0) {
    console.log(`⚠️ 动态阈值过滤掉所有结果，返回前${topN}个结果`);
    const indexList = sortedResults.slice(0, topN).map(res => res.index);
    return chunks.filter((chunk, index) => indexList.includes(index));
  }

  console.log(`📊 阈值过滤后剩余: ${filteredResults.length} 个结果`);

  // 筛选满足条件的chunks
  const indexList = filteredResults
    .slice(0, topN) // 限制最大返回数量
    .map(res => res.index);

  return chunks.filter((chunk, index) => indexList.includes(index));
}

/**
 * 根据文件类型查询相似片段
 */
async function findSimilarChunks(description, fileType = null, limit = 10) {
  const queryEmbedding = await getKnowledgeEmbedding(description);

  let sql, args;

  if (fileType) {
    // 查询指定fileType的file
    const files = await libsqlClient.execute({
      sql: `SELECT id FROM user_file WHERE file_type = ?`,
      args: [fileType]
    });

    if (!files.rows.length) {
      return [];
    }

    const fileIds = files.rows.map(row => row.id);

    sql = `WITH vector_scores AS (
      SELECT rowid AS id,
             file_id,
             content,
             embedding,
             1 - vector_distance_cos(embedding, vector32(?)) AS similarity
      FROM user_file_embd
      WHERE file_id IN (${Array(fileIds.length).fill('?').join(',')})
      ORDER BY similarity DESC
      LIMIT ?
    )
    SELECT v.id,
           v.file_id AS fileId,
           v.content AS content,
           v.similarity,
           f.file_name,
           f.file_path AS filePath
    FROM vector_scores v
    LEFT JOIN user_file f ON v.file_id = f.id`;

    args = [JSON.stringify(Array.from(queryEmbedding)), ...fileIds, limit];
  } else {
    // 查询所有文件
    sql = `WITH vector_scores AS (
      SELECT rowid AS id,
             file_id,
             content,
             embedding,
             1 - vector_distance_cos(embedding, vector32(?)) AS similarity
      FROM user_file_embd
      ORDER BY similarity DESC
      LIMIT ?
    )
    SELECT v.id,
           v.file_id AS fileId,
           v.content AS content,
           v.similarity,
           f.file_name,
           f.file_path AS filePath
    FROM vector_scores v
    LEFT JOIN user_file f ON v.file_id = f.id`;

    args = [JSON.stringify(Array.from(queryEmbedding)), limit];
  }

  const results = await libsqlClient.execute({ sql, args });
  return results.rows;
}

/**
 * 搜索知识库 - 增强版本，支持重排序和动态阈值
 */
async function searchKnowledge(query, limit = null, fileType = null) {
  try {
    // 只有在数据库还未初始化时才进行初始化
    if (!isKnowledgeInitialized) {
      console.log('🔧 知识库未初始化，开始初始化...')
      await initKnowledgeDatabase()
    }

    const searchLimit = limit || KNOWLEDGE_CONFIG.search.defaultLimit

    console.log(`🔍 搜索知识库: "${query}"${fileType ? ` (文件类型: ${fileType})` : ''}`)

    // 首先检查数据库中是否有数据
    const countResult = await libsqlClient.execute('SELECT COUNT(*) as count FROM user_file_embd')
    const totalEmbeddings = countResult.rows[0].count
    console.log(`📊 数据库中共有 ${totalEmbeddings} 个embedding片段`)

    if (totalEmbeddings === 0) {
      console.log('⚠️ 数据库中没有embedding数据，请先索引文档')
      return []
    }

    // 尝试使用增强搜索
    let searchResults = []
    let useVectorSearch = true

    try {
      // 第一步：使用向量搜索找到候选结果
      console.log('🔍 第一步：向量搜索候选结果...')
      let similarChunks = await findSimilarChunks(query, fileType, searchLimit * 2)

      console.log(`🔍 向量搜索结果: ${similarChunks.length} 个候选`)

      if (similarChunks.length === 0) {
        console.log('⚠️ 向量搜索没有找到结果')
        return []
      }

      // 第二步：使用重排序模型优化结果
      console.log('🔄 第二步：重排序优化结果...')
      const rerankedChunks = await rerank(similarChunks, query)

      console.log(`✨ 重排序后结果: ${rerankedChunks.length} 个`)

      // 转换为标准格式
      searchResults = rerankedChunks.map(row => ({
        id: row.id,
        file_id: row.fileId || row.file_id,
        content: row.content,
        similarity: row.similarity,
        file_name: row.file_name,
        source_file_path: row.filePath || row.source_file_path
      }))

    } catch (vectorError) {
      console.error('⚠️ 增强搜索失败，降级到简单文本搜索:', vectorError.message)
      useVectorSearch = false

      // 降级到基于文本的简单搜索
      try {
        const textSearchResults = await libsqlClient.execute({
          sql: `
            SELECT rowid as id,
                   file_id,
                   content as content,
                   0.5 as similarity
            FROM user_file_embd
            WHERE content LIKE ? OR content LIKE ?
            ORDER BY
              CASE
                WHEN content LIKE ? THEN 1
                WHEN content LIKE ? THEN 2
                ELSE 3
              END
            LIMIT ?
          `,
          args: [
            `%${query}%`,
            `%${query.split(' ').join('%')}%`,
            `%${query}%`,
            `%${query.split(' ').join('%')}%`,
            searchLimit
          ]
        })

        searchResults = textSearchResults.rows.map(row => ({
          id: row.id,
          file_id: row.file_id,
          content: row.content,
          similarity: row.similarity
        }))

        console.log(`📝 文本搜索结果: ${searchResults.length} 个`)
      } catch (textError) {
        console.error('❌ 文本搜索也失败:', textError.message)
        searchResults = []
      }
    }

    if (useVectorSearch) {
      console.log(`✅ 使用增强向量搜索，最终返回 ${searchResults.length} 个相关文档片段`)
    } else {
      console.log(`✅ 使用文本搜索，最终返回 ${searchResults.length} 个相关文档片段`)
    }

    // 应用最终相似度过滤
    const finalThreshold = KNOWLEDGE_CONFIG.search.similarityThreshold || 0.5
    const filteredResults = searchResults.filter(result => result.similarity >= finalThreshold)

    console.log(`📊 相似度过滤: 原始结果 ${searchResults.length} 个，过滤后 ${filteredResults.length} 个 (阈值: ${(finalThreshold * 100).toFixed(0)}%)`)

    // 输出详细的搜索结果信息
    filteredResults.forEach((result, index) => {
      console.log(`  ${index + 1}. 相似度: ${(result.similarity * 100).toFixed(1)}%`)
      if (result.file_name) {
        console.log(`     来源文档: ${result.file_name}`)
      }
      console.log(`     内容预览: ${result.content.substring(0, 80)}...`)
    })

    return filteredResults
  } catch (error) {
    console.error('❌ 知识库搜索失败:', error)
    console.error('❌ 错误详情:', error.message)
    console.error('❌ 错误堆栈:', error.stack)
    return []
  }
}

/**
 * 获取知识库统计信息
 */
async function getKnowledgeStats() {
  try {
    if (!isKnowledgeInitialized) {
      console.log('🔧 知识库未初始化，开始初始化...')
      await initKnowledgeDatabase()
    }

    const fileCountResult = await libsqlClient.execute(
      'SELECT COUNT(*) as count FROM user_file'
    )

    const segmentCountResult = await libsqlClient.execute(
      'SELECT COUNT(*) as count FROM user_file_embd'
    )

    const totalFiles = fileCountResult.rows[0]?.count || 0
    const totalSegments = segmentCountResult.rows[0]?.count || 0

    console.log(`📊 知识库统计: ${totalFiles} 个文件, ${totalSegments} 个片段`)

    return {
      totalFiles: Number(totalFiles),
      totalSegments: Number(totalSegments)
    }
  } catch (error) {
    console.error('❌ 获取知识库统计失败:', error)
    return { totalFiles: 0, totalSegments: 0 }
  }
}

// 应用管理器类
class AppManager {
  constructor() {
    this.mainWindow = null
    this.floatingWindow = null
    this.tray = null
    this.mcpManager = null
    this.emailService = null
    this.calendarService = null
    this.isLoggedIn = false
    this.hasShownTrayNotification = false
  }

  async initialize() {
    console.log('🚀 开始初始化应用管理器...')

    // 设置控制台转发（暂时跳过）
    // setupConsoleForwarding()

    // 创建主窗口
    await this.createMainWindow()

    // 创建系统托盘
    this.createTray()

    // 设置应用菜单
    this.setupMenu()

    // 初始化服务
    await this.initializeServices()

    // 设置IPC处理程序
    this.setupIPC()

    console.log('✅ 应用管理器初始化完成')
  }

  async initializeServices() {
    console.log('🔧 开始初始化服务...')

    try {
      // 发送初始化状态消息
      this.sendStatusMessage('正在初始化服务...')

      // 暂时跳过服务初始化，直到模块依赖问题解决
      console.log('⚠️ 服务初始化暂时跳过（模块依赖问题）')

      // 模拟一些初始化时间，让用户看到加载过程
      await new Promise(resolve => setTimeout(resolve, 1000))

      // TODO: 重新启用服务初始化
      // this.mcpManager = new MCPClientManager()
      // await this.mcpManager.initialize()
      // this.emailService = new EmailService()
      // await this.emailService.initialize()
      // this.calendarService = new OutlookCalendarService()
      // await this.calendarService.initialize()
      // setupKnowledgeIPC()

      console.log('✅ 基础服务初始化完成')

      // 发送应用初始化完成消息
      this.sendStatusMessage('应用初始化完成')

    } catch (error) {
      console.error('❌ 服务初始化失败:', error)
      // 即使服务初始化失败，也不要阻止应用启动
      this.sendStatusMessage('应用初始化完成')
    }
  }

  // 发送状态消息到渲染进程
  sendStatusMessage(message) {
    console.log('📤 发送状态消息:', message)
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.webContents.send('status-message', message)
    }
  }

  async createMainWindow() {
    console.log('🪟 创建主窗口...')

    this.mainWindow = new BrowserWindow({
      width: 1200,
      height: 800,
      minWidth: 800,
      minHeight: 600,
      webPreferences: {
        preload: isDev
          ? join(process.cwd(), 'src/preload/preload.js')
          : join(__dirname, 'preload.js'),
        nodeIntegration: false,
        contextIsolation: true,
        webSecurity: false, // 允许跨域请求以支持各种AI服务API
        allowRunningInsecureContent: true, // 允许不安全内容以支持各种API
        experimentalFeatures: true, // 启用实验性功能支持WebRTC等
        additionalArguments: ['--enable-features=WebRTC-H264WithOpenH264FFmpeg'] // 支持WebRTC
      },
      icon: isDev ? join(process.cwd(), 'public/assets/logo.ico') : join(__dirname, '../public/assets/logo.ico'),
      show: false,
      titleBarStyle: 'default'
    })

    // 设置全局引用
    global.mainWindow = this.mainWindow

    // 加载应用
    if (isDev) {
      this.mainWindow.loadURL('http://localhost:6913')
      // 开发环境自动打开开发者工具
      this.mainWindow.webContents.openDevTools()
    } else {
      this.mainWindow.loadFile(join(__dirname, '../build/index.html'))
    }

    // 窗口准备显示时显示窗口
    this.mainWindow.once('ready-to-show', () => {
      console.log('🪟 主窗口准备就绪，显示窗口')
      this.mainWindow.show()
      this.checkLoginStatus()
    })

    // 窗口关闭时隐藏到托盘
    this.mainWindow.on('close', (event) => {
      if (!app.isQuiting) {
        event.preventDefault()
        this.mainWindow.hide()

        if (!this.hasShownTrayNotification) {
          this.showTrayNotification()
          this.hasShownTrayNotification = true
        }
      }
    })

    console.log('✅ 主窗口创建完成')
  }

  createTray() {
    console.log('🔔 创建系统托盘...')

    const trayIconPath = isDev ? join(process.cwd(), 'public/assets/logo.ico') : join(__dirname, '../public/assets/logo.ico')
    this.tray = new Tray(trayIconPath)

    const contextMenu = Menu.buildFromTemplate([
      {
        label: '显示主窗口',
        click: () => {
          this.mainWindow.show()
          this.mainWindow.focus()
        }
      },
      {
        label: '退出',
        click: () => {
          app.isQuiting = true
          app.quit()
        }
      }
    ])

    this.tray.setToolTip('犇犇AI助手')
    this.tray.setContextMenu(contextMenu)

    this.tray.on('double-click', () => {
      this.mainWindow.show()
      this.mainWindow.focus()
    })

    console.log('✅ 系统托盘创建完成')
  }

  setupMenu() {
    if (process.platform === 'darwin') {
      const template = [
        {
          label: app.getName(),
          submenu: [
            { role: 'about' },
            { type: 'separator' },
            { role: 'quit' }
          ]
        }
      ]
      const menu = Menu.buildFromTemplate(template)
      Menu.setApplicationMenu(menu)
    } else {
      Menu.setApplicationMenu(null)
    }
  }

  setupIPC() {
    console.log('🔧 设置IPC处理程序...')

    // 基本应用信息
    ipcMain.handle('get-app-info', () => {
      return {
        version: app.getVersion(),
        name: app.getName(),
        isLoggedIn: this.isLoggedIn
      }
    })

    // 窗口控制
    ipcMain.handle('minimize-to-tray', () => {
      this.mainWindow.hide()
      return { success: true }
    })

    ipcMain.handle('quit-app', () => {
      app.isQuiting = true
      app.quit()
    })

    // MCP相关IPC
    if (this.mcpManager) {
      ipcMain.handle('call-mcp-tool', async (_, toolName, args) => {
        try {
          return await this.mcpManager.callTool(toolName, args)
        } catch (error) {
          console.error('MCP工具调用失败:', error)
          return { error: error.message }
        }
      })

      ipcMain.handle('get-mcp-tools', async () => {
        try {
          return await this.mcpManager.getAvailableTools()
        } catch (error) {
          console.error('获取MCP工具失败:', error)
          return { error: error.message }
        }
      })
    }

    // 邮件服务IPC
    if (this.emailService) {
      ipcMain.handle('check-emails', async () => {
        try {
          return await this.emailService.checkEmails()
        } catch (error) {
          console.error('检查邮件失败:', error)
          return { error: error.message }
        }
      })
    }

    // 日历服务IPC
    if (this.calendarService) {
      ipcMain.handle('get-calendar-events', async (_, startDate, endDate) => {
        try {
          return await this.calendarService.getEvents(startDate, endDate)
        } catch (error) {
          console.error('获取日历事件失败:', error)
          return { error: error.message }
        }
      })
    }

    // 状态消息广播IPC
    ipcMain.handle('send-status-message', async (_, message) => {
      try {
        console.log('🔄 收到状态消息广播请求:', message)
        this.sendStatusMessage(message)
        return { success: true }
      } catch (error) {
        console.error('❌ 发送状态消息失败:', error)
        return { success: false, error: error.message }
      }
    })

    // 开发者工具切换IPC
    ipcMain.on('toggle-dev-tools', (_, enabled) => {
      console.log('🔧 切换开发者工具:', enabled)

      // 打开/关闭主窗口的开发者工具
      if (this.mainWindow) {
        if (enabled) {
          this.mainWindow.webContents.openDevTools()
        } else {
          this.mainWindow.webContents.closeDevTools()
        }
      }
    })

    // === 知识库相关的IPC处理器 ===

    // 初始化知识库
    ipcMain.handle('knowledge-init', async () => {
      try {
        console.log('🧠 收到知识库初始化请求')
        const result = await initKnowledgeDatabase()
        console.log('🧠 知识库初始化结果:', result)
        return result
      } catch (error) {
        console.error('🧠 知识库初始化失败:', error)
        return { success: false, error: error.message }
      }
    })

    // 获取知识库统计
    ipcMain.handle('knowledge-stats', async () => {
      try {
        console.log('🧠 收到知识库统计请求')
        const stats = await getKnowledgeStats()
        console.log('🧠 知识库统计结果:', stats)
        return stats
      } catch (error) {
        console.error('🧠 获取知识库统计失败:', error)
        return { totalFiles: 0, totalSegments: 0 }
      }
    })

    // 搜索知识库
    ipcMain.handle('knowledge-search', async (_, query, limit, fileType) => {
      try {
        console.log(`🧠 收到知识库搜索请求: "${query}", 限制: ${limit}${fileType ? `, 文件类型: ${fileType}` : ''}`)
        const results = await searchKnowledge(query, limit, fileType)
        console.log(`🧠 知识库搜索完成，找到 ${results.length} 个结果`)
        return results
      } catch (error) {
        console.error('🧠 知识库搜索失败:', error)
        return []
      }
    })

    // 列出知识库文件
    ipcMain.handle('knowledge-list-files', async (_, fileType, fileName, pageSize = 10, pageNum = 1) => {
      try {
        console.log(`🧠 收到列出文件请求: fileType=${fileType}, fileName=${fileName}, pageSize=${pageSize}, pageNum=${pageNum}`)

        if (!isKnowledgeInitialized) {
          console.log('🧠 知识库未初始化，开始初始化...')
          await initKnowledgeDatabase()
        }

        // 构建查询条件
        let whereConditions = []
        let args = []

        if (fileType !== null && fileType !== undefined) {
          whereConditions.push('file_type = ?')
          args.push(fileType)
        }

        if (fileName && fileName.trim()) {
          whereConditions.push('file_name LIKE ?')
          args.push(`%${fileName.trim()}%`)
        }

        const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : ''

        // 获取总数
        const countSql = `SELECT COUNT(*) as total FROM user_file ${whereClause}`
        const countResult = await libsqlClient.execute({ sql: countSql, args })
        const total = Number(countResult.rows[0]?.total || 0)

        // 获取分页数据
        const offset = (pageNum - 1) * pageSize
        const dataSql = `
          SELECT id, file_name, file_path, file_type, file_size, create_time, update_time
          FROM user_file ${whereClause}
          ORDER BY create_time DESC
          LIMIT ? OFFSET ?
        `
        const dataResult = await libsqlClient.execute({
          sql: dataSql,
          args: [...args, pageSize, offset]
        })

        const files = dataResult.rows.map(row => ({
          id: row.id,
          fileName: row.file_name,
          filePath: row.file_path,
          fileType: row.file_type,
          fileSize: row.file_size,
          createTime: row.create_time,
          updateTime: row.update_time
        }))

        console.log(`🧠 找到 ${files.length} 个文件，总计 ${total} 个`)
        return { files, total, pageSize, pageNum }
      } catch (error) {
        console.error('🧠 列出文件失败:', error)
        return { files: [], total: 0, pageSize, pageNum }
      }
    })

    // 删除知识库文件
    ipcMain.handle('knowledge-delete-file', async (_, fileId) => {
      try {
        console.log(`🧠 收到删除文件请求: ${fileId}`)

        if (!isKnowledgeInitialized) {
          console.log('🧠 知识库未初始化，开始初始化...')
          await initKnowledgeDatabase()
        }

        // 删除文件的embedding数据
        await libsqlClient.execute({
          sql: 'DELETE FROM user_file_embd WHERE file_id = ?',
          args: [fileId]
        })

        // 删除文件记录
        await libsqlClient.execute({
          sql: 'DELETE FROM user_file WHERE id = ?',
          args: [fileId]
        })

        console.log(`✅ 文件 ${fileId} 删除成功`)
        return { success: true }
      } catch (error) {
        console.error(`❌ 删除文件 ${fileId} 失败:`, error)
        return { success: false, error: error.message }
      }
    })

    // 获取用户数据路径
    ipcMain.handle('get-path', async (_, pathName) => {
      console.log('🧠 收到获取路径请求:', pathName)
      try {
        return app.getPath(pathName)
      } catch (error) {
        console.error('❌ 获取路径失败:', error)
        return null
      }
    })

    // 更新知识库配置
    ipcMain.handle('knowledge-update-config', async (_, newConfig) => {
      try {
        console.log('🧠 收到知识库配置更新请求:', newConfig)
        updateKnowledgeConfig(newConfig)
        return { success: true }
      } catch (error) {
        console.error('🧠 更新知识库配置失败:', error)
        return { success: false, error: error.message }
      }
    })

    // 配置管理相关的IPC处理
    ipcMain.handle('update-chat-config', async (_, config) => {
      try {
        // 更新全局配置
        if (config.embedding) {
          KNOWLEDGE_CONFIG.embedding = { ...KNOWLEDGE_CONFIG.embedding, ...config.embedding }
        }
        if (config.chat) {
          KNOWLEDGE_CONFIG = { ...KNOWLEDGE_CONFIG, ...config }
        } else {
          // 如果是聊天配置更新，更新对应的embedding配置
          if (config.baseURL) KNOWLEDGE_CONFIG.embedding.baseURL = config.baseURL
          if (config.apiKey) KNOWLEDGE_CONFIG.embedding.apiKey = config.apiKey
        }

        // 保存到全局变量，供其他地方使用
        global.sharedKnowledgeConfig = KNOWLEDGE_CONFIG

        console.log('✅ 主进程配置已更新:', KNOWLEDGE_CONFIG)
        return { success: true }
      } catch (error) {
        console.error('❌ 更新主进程配置失败:', error)
        return { success: false, error: error.message }
      }
    })

    // 获取应用版本
    ipcMain.handle('get-app-version', async () => {
      console.log('📱 收到获取应用版本请求')
      return app.getVersion()
    })

    // 设置登录状态（临时实现）
    ipcMain.handle('set-login-status', async (_, status) => {
      console.log('🔑 收到设置登录状态请求:', status)
      return { success: true }
    })

    // === 添加缺失的基础IPC处理器 ===

    // 登录相关
    ipcMain.handle('login', async (_, credentials) => {
      console.log('🔑 收到登录请求:', credentials.username)
      if (credentials.username && credentials.password) {
        this.isLoggedIn = true
        return { success: true, message: '登录成功', user: { username: credentials.username } }
      }
      return { success: false, message: '用户名或密码错误' }
    })

    ipcMain.handle('logout', async () => {
      console.log('🚪 收到注销请求')
      this.isLoggedIn = false
      return { success: true }
    })

    ipcMain.handle('get-login-status', () => {
      console.log('🔍 收到获取登录状态请求')
      return this.isLoggedIn || false
    })

    // 窗口控制
    ipcMain.on('show-main-window', (_, page) => {
      console.log('🪟 显示主窗口:', page)
      if (this.mainWindow) {
        this.mainWindow.show()
        this.mainWindow.focus()
        if (page) {
          this.mainWindow.webContents.send('navigate-to', page)
        }
      } else {
        this.createMainWindow()
      }
    })

    ipcMain.on('hide-main-window', () => {
      console.log('🪟 隐藏主窗口')
      if (this.mainWindow) {
        this.mainWindow.hide()
      }
    })

    ipcMain.on('close-app', () => {
      console.log('🚪 关闭应用')
      app.quit()
    })

    // 打开URL
    ipcMain.handle('open-url', async (_, url) => {
      try {
        console.log('🔗 打开URL:', url)
        await shell.openExternal(url)
        return { success: true }
      } catch (error) {
        console.error('🔗 打开URL失败:', error)
        return { success: false, error: error.message }
      }
    })

    // 语音播放
    ipcMain.handle('speak-welcome-message', async (_, options) => {
      try {
        console.log('🔊 收到欢迎语音播放请求:', options)
        // 这里可以添加实际的语音播放逻辑
        return { success: true }
      } catch (error) {
        console.error('🔊 播放欢迎语音失败:', error)
        return { success: false, error: error.message }
      }
    })

    console.log('✅ IPC处理程序设置完成')
  }

  showTrayNotification() {
    if (Notification.isSupported()) {
      const notification = new Notification({
        title: '犇犇AI助手',
        body: '应用已最小化到系统托盘，双击托盘图标可重新打开',
        icon: isDev ? join(process.cwd(), 'public/assets/logo.ico') : join(__dirname, '../public/assets/logo.ico')
      })
      notification.show()
    }
  }

  checkLoginStatus() {
    const store = new Store()
    const userToken = store.get('userAuthToken')

    if (userToken) {
      console.log('🔑 发现存储的用户token，用户已登录')
      this.isLoggedIn = true
    } else {
      console.log('🔑 未发现用户token，用户未登录')
      this.isLoggedIn = false
    }
  }

  async cleanup() {
    console.log('🧹 开始清理应用管理器...')

    // 清理服务
    if (this.mcpManager) {
      await this.mcpManager.cleanup()
      this.mcpManager = null
    }

    if (this.emailService) {
      if (typeof this.emailService.cleanup === 'function') {
        this.emailService.cleanup()
      }
      this.emailService = null
    }

    if (this.calendarService) {
      if (typeof this.calendarService.cleanup === 'function') {
        this.calendarService.cleanup()
      }
      this.calendarService = null
    }

    // 清理托盘
    if (this.tray) {
      this.tray.destroy()
      this.tray = null
    }

    global.mainWindow = null
    console.log('✅ 应用管理器清理完成')
  }
}

// 创建应用管理器实例
let appManager = null

// 应用事件处理
app.whenReady().then(async () => {
  console.log('🚀 Electron应用已准备就绪')
  
  try {
    // 创建并初始化应用管理器
    appManager = new AppManager()
    await appManager.initialize()
    
    console.log('✅ 应用初始化完成')
  } catch (error) {
    console.error('❌ 应用初始化失败:', error)
    // 即使初始化失败，也不要退出应用，让用户能够看到错误信息
  }
})

// 当所有窗口都关闭时
app.on('window-all-closed', () => {
  // 在macOS上，应用通常会保持活跃状态，即使没有打开的窗口
  // 除非用户明确使用Cmd + Q退出应用
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

// 当应用被激活时（macOS）
app.on('activate', async () => {
  // 在macOS上，当点击dock图标且没有其他窗口打开时，
  // 通常会重新创建一个窗口
  if (appManager && appManager.mainWindow === null) {
    await appManager.createMainWindow()
  }
})

// 应用即将退出时的清理工作
app.on('before-quit', async (event) => {
  console.log('🧹 应用即将退出，开始清理...')
  
  if (appManager) {
    // 防止默认的退出行为，先进行清理
    event.preventDefault()
    
    try {
      await appManager.cleanup()
      console.log('✅ 应用清理完成')
    } catch (error) {
      console.error('❌ 应用清理失败:', error)
    } finally {
      // 清理完成后真正退出应用
      app.exit(0)
    }
  }
})

// 处理未捕获的异常
process.on('uncaughtException', (error) => {
  console.error('❌ 未捕获的异常:', error)
  console.error('❌ 错误堆栈:', error.stack)
  
  // 在生产环境中，可能需要重启应用或显示错误对话框
  if (!isDev) {
    // 可以在这里添加错误报告逻辑
    console.error('🚨 生产环境中发生未捕获异常，应用可能不稳定')
  }
})

// 处理未处理的Promise拒绝
process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ 未处理的Promise拒绝:', reason)
  console.error('❌ Promise:', promise)
  
  if (!isDev) {
    console.error('🚨 生产环境中发生未处理的Promise拒绝')
  }
})

// 设置应用的安全策略
app.on('web-contents-created', (_, contents) => {
  // 防止新窗口的创建
  contents.on('new-window', (event, navigationUrl) => {
    event.preventDefault()
    console.warn('🚨 阻止了新窗口的创建:', navigationUrl)
  })
  
  // 防止导航到外部URL
  contents.on('will-navigate', (event, navigationUrl) => {
    const parsedUrl = new URL(navigationUrl)
    
    // 只允许本地开发服务器和本地文件
    if (parsedUrl.origin !== 'http://localhost:6913' &&
        parsedUrl.protocol !== 'file:') {
      event.preventDefault()
      console.warn('🚨 阻止了导航到外部URL:', navigationUrl)
    }
  })
})

// 导出应用管理器实例（用于测试或调试）
module.exports = {
  getAppManager: () => appManager,
  isDev
}
