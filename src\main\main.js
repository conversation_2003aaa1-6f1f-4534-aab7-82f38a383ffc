const { app, B<PERSON>erWindow, <PERSON><PERSON>, <PERSON>ray, ipc<PERSON>ain, Notification } = require('electron')
const { join } = require('path')
const Store = require('electron-store')

// 暂时注释掉模块导入，直到构建系统能够正确处理
// const MCPClientManager = require('./mcp-manager')
// const EmailService = require('./email-service')
// const OutlookCalendarService = require('./calendar-service')
// const { setupKnowledgeIPC } = require('./knowledge-service')
// const { setupConsoleForwarding } = require('./utils')

// 检查是否为开发环境
const isDev = process.env.NODE_ENV === 'development'

// 应用管理器类
class AppManager {
  constructor() {
    this.mainWindow = null
    this.floatingWindow = null
    this.tray = null
    this.mcpManager = null
    this.emailService = null
    this.calendarService = null
    this.isLoggedIn = false
    this.hasShownTrayNotification = false
  }

  async initialize() {
    console.log('🚀 开始初始化应用管理器...')

    // 设置控制台转发（暂时跳过）
    // setupConsoleForwarding()

    // 创建主窗口
    await this.createMainWindow()

    // 创建系统托盘
    this.createTray()

    // 设置应用菜单
    this.setupMenu()

    // 初始化服务
    await this.initializeServices()

    // 设置IPC处理程序
    this.setupIPC()

    console.log('✅ 应用管理器初始化完成')
  }

  async initializeServices() {
    console.log('🔧 开始初始化服务...')

    try {
      // 发送初始化状态消息
      this.sendStatusMessage('正在初始化服务...')

      // 暂时跳过服务初始化，直到模块依赖问题解决
      console.log('⚠️ 服务初始化暂时跳过（模块依赖问题）')

      // 模拟一些初始化时间，让用户看到加载过程
      await new Promise(resolve => setTimeout(resolve, 1000))

      // TODO: 重新启用服务初始化
      // this.mcpManager = new MCPClientManager()
      // await this.mcpManager.initialize()
      // this.emailService = new EmailService()
      // await this.emailService.initialize()
      // this.calendarService = new OutlookCalendarService()
      // await this.calendarService.initialize()
      // setupKnowledgeIPC()

      console.log('✅ 基础服务初始化完成')

      // 发送应用初始化完成消息
      this.sendStatusMessage('应用初始化完成')

    } catch (error) {
      console.error('❌ 服务初始化失败:', error)
      // 即使服务初始化失败，也不要阻止应用启动
      this.sendStatusMessage('应用初始化完成')
    }
  }

  // 发送状态消息到渲染进程
  sendStatusMessage(message) {
    console.log('📤 发送状态消息:', message)
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.webContents.send('status-message', message)
    }
  }

  async createMainWindow() {
    console.log('🪟 创建主窗口...')

    this.mainWindow = new BrowserWindow({
      width: 1200,
      height: 800,
      minWidth: 800,
      minHeight: 600,
      webPreferences: {
        nodeIntegration: true,
        contextIsolation: false,
        enableRemoteModule: true,
        webSecurity: false
      },
      icon: isDev ? join(process.cwd(), 'public/assets/logo.ico') : join(__dirname, '../public/assets/logo.ico'),
      show: false,
      titleBarStyle: 'default'
    })

    // 设置全局引用
    global.mainWindow = this.mainWindow

    // 加载应用
    if (isDev) {
      this.mainWindow.loadURL('http://localhost:6913')
    } else {
      this.mainWindow.loadFile(join(__dirname, '../build/index.html'))
    }

    // 窗口准备显示时显示窗口
    this.mainWindow.once('ready-to-show', () => {
      console.log('🪟 主窗口准备就绪，显示窗口')
      this.mainWindow.show()
      this.checkLoginStatus()
    })

    // 窗口关闭时隐藏到托盘
    this.mainWindow.on('close', (event) => {
      if (!app.isQuiting) {
        event.preventDefault()
        this.mainWindow.hide()

        if (!this.hasShownTrayNotification) {
          this.showTrayNotification()
          this.hasShownTrayNotification = true
        }
      }
    })

    console.log('✅ 主窗口创建完成')
  }

  createTray() {
    console.log('🔔 创建系统托盘...')

    const trayIconPath = isDev ? join(process.cwd(), 'public/assets/logo.ico') : join(__dirname, '../public/assets/logo.ico')
    this.tray = new Tray(trayIconPath)

    const contextMenu = Menu.buildFromTemplate([
      {
        label: '显示主窗口',
        click: () => {
          this.mainWindow.show()
          this.mainWindow.focus()
        }
      },
      {
        label: '退出',
        click: () => {
          app.isQuiting = true
          app.quit()
        }
      }
    ])

    this.tray.setToolTip('犇犇AI助手')
    this.tray.setContextMenu(contextMenu)

    this.tray.on('double-click', () => {
      this.mainWindow.show()
      this.mainWindow.focus()
    })

    console.log('✅ 系统托盘创建完成')
  }

  setupMenu() {
    if (process.platform === 'darwin') {
      const template = [
        {
          label: app.getName(),
          submenu: [
            { role: 'about' },
            { type: 'separator' },
            { role: 'quit' }
          ]
        }
      ]
      const menu = Menu.buildFromTemplate(template)
      Menu.setApplicationMenu(menu)
    } else {
      Menu.setApplicationMenu(null)
    }
  }

  setupIPC() {
    console.log('🔧 设置IPC处理程序...')

    // 基本应用信息
    ipcMain.handle('get-app-info', () => {
      return {
        version: app.getVersion(),
        name: app.getName(),
        isLoggedIn: this.isLoggedIn
      }
    })

    // 窗口控制
    ipcMain.handle('minimize-to-tray', () => {
      this.mainWindow.hide()
      return { success: true }
    })

    ipcMain.handle('quit-app', () => {
      app.isQuiting = true
      app.quit()
    })

    // MCP相关IPC
    if (this.mcpManager) {
      ipcMain.handle('call-mcp-tool', async (_, toolName, args) => {
        try {
          return await this.mcpManager.callTool(toolName, args)
        } catch (error) {
          console.error('MCP工具调用失败:', error)
          return { error: error.message }
        }
      })

      ipcMain.handle('get-mcp-tools', async () => {
        try {
          return await this.mcpManager.getAvailableTools()
        } catch (error) {
          console.error('获取MCP工具失败:', error)
          return { error: error.message }
        }
      })
    }

    // 邮件服务IPC
    if (this.emailService) {
      ipcMain.handle('check-emails', async () => {
        try {
          return await this.emailService.checkEmails()
        } catch (error) {
          console.error('检查邮件失败:', error)
          return { error: error.message }
        }
      })
    }

    // 日历服务IPC
    if (this.calendarService) {
      ipcMain.handle('get-calendar-events', async (_, startDate, endDate) => {
        try {
          return await this.calendarService.getEvents(startDate, endDate)
        } catch (error) {
          console.error('获取日历事件失败:', error)
          return { error: error.message }
        }
      })
    }

    // 状态消息广播IPC
    ipcMain.handle('send-status-message', async (_, message) => {
      try {
        console.log('🔄 收到状态消息广播请求:', message)
        this.sendStatusMessage(message)
        return { success: true }
      } catch (error) {
        console.error('❌ 发送状态消息失败:', error)
        return { success: false, error: error.message }
      }
    })

    console.log('✅ IPC处理程序设置完成')
  }

  showTrayNotification() {
    if (Notification.isSupported()) {
      const notification = new Notification({
        title: '犇犇AI助手',
        body: '应用已最小化到系统托盘，双击托盘图标可重新打开',
        icon: isDev ? join(process.cwd(), 'public/assets/logo.ico') : join(__dirname, '../public/assets/logo.ico')
      })
      notification.show()
    }
  }

  checkLoginStatus() {
    const store = new Store()
    const userToken = store.get('userAuthToken')

    if (userToken) {
      console.log('🔑 发现存储的用户token，用户已登录')
      this.isLoggedIn = true
    } else {
      console.log('🔑 未发现用户token，用户未登录')
      this.isLoggedIn = false
    }
  }

  async cleanup() {
    console.log('🧹 开始清理应用管理器...')

    // 清理服务
    if (this.mcpManager) {
      await this.mcpManager.cleanup()
      this.mcpManager = null
    }

    if (this.emailService) {
      if (typeof this.emailService.cleanup === 'function') {
        this.emailService.cleanup()
      }
      this.emailService = null
    }

    if (this.calendarService) {
      if (typeof this.calendarService.cleanup === 'function') {
        this.calendarService.cleanup()
      }
      this.calendarService = null
    }

    // 清理托盘
    if (this.tray) {
      this.tray.destroy()
      this.tray = null
    }

    global.mainWindow = null
    console.log('✅ 应用管理器清理完成')
  }
}

// 创建应用管理器实例
let appManager = null

// 应用事件处理
app.whenReady().then(async () => {
  console.log('🚀 Electron应用已准备就绪')
  
  try {
    // 创建并初始化应用管理器
    appManager = new AppManager()
    await appManager.initialize()
    
    console.log('✅ 应用初始化完成')
  } catch (error) {
    console.error('❌ 应用初始化失败:', error)
    // 即使初始化失败，也不要退出应用，让用户能够看到错误信息
  }
})

// 当所有窗口都关闭时
app.on('window-all-closed', () => {
  // 在macOS上，应用通常会保持活跃状态，即使没有打开的窗口
  // 除非用户明确使用Cmd + Q退出应用
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

// 当应用被激活时（macOS）
app.on('activate', async () => {
  // 在macOS上，当点击dock图标且没有其他窗口打开时，
  // 通常会重新创建一个窗口
  if (appManager && appManager.mainWindow === null) {
    await appManager.createMainWindow()
  }
})

// 应用即将退出时的清理工作
app.on('before-quit', async (event) => {
  console.log('🧹 应用即将退出，开始清理...')
  
  if (appManager) {
    // 防止默认的退出行为，先进行清理
    event.preventDefault()
    
    try {
      await appManager.cleanup()
      console.log('✅ 应用清理完成')
    } catch (error) {
      console.error('❌ 应用清理失败:', error)
    } finally {
      // 清理完成后真正退出应用
      app.exit(0)
    }
  }
})

// 处理未捕获的异常
process.on('uncaughtException', (error) => {
  console.error('❌ 未捕获的异常:', error)
  console.error('❌ 错误堆栈:', error.stack)
  
  // 在生产环境中，可能需要重启应用或显示错误对话框
  if (!isDev) {
    // 可以在这里添加错误报告逻辑
    console.error('🚨 生产环境中发生未捕获异常，应用可能不稳定')
  }
})

// 处理未处理的Promise拒绝
process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ 未处理的Promise拒绝:', reason)
  console.error('❌ Promise:', promise)
  
  if (!isDev) {
    console.error('🚨 生产环境中发生未处理的Promise拒绝')
  }
})

// 设置应用的安全策略
app.on('web-contents-created', (_, contents) => {
  // 防止新窗口的创建
  contents.on('new-window', (event, navigationUrl) => {
    event.preventDefault()
    console.warn('🚨 阻止了新窗口的创建:', navigationUrl)
  })
  
  // 防止导航到外部URL
  contents.on('will-navigate', (event, navigationUrl) => {
    const parsedUrl = new URL(navigationUrl)
    
    // 只允许本地开发服务器和本地文件
    if (parsedUrl.origin !== 'http://localhost:6913' &&
        parsedUrl.protocol !== 'file:') {
      event.preventDefault()
      console.warn('🚨 阻止了导航到外部URL:', navigationUrl)
    }
  })
})

// 导出应用管理器实例（用于测试或调试）
module.exports = {
  getAppManager: () => appManager,
  isDev
}
