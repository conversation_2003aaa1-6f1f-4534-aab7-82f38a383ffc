const { ipcMain } = require('electron')
const Store = require('electron-store')

// === Outlook日历同步服务管理器 ===
class OutlookCalendarService {
  constructor(mcpManager, mainWindow) {
    this.mcpManager = mcpManager
    this.mainWindow = mainWindow
    this.isInitialized = false
    this.syncedTodos = new Map() // 记录已同步到日历的待办事项

    // 添加持久化存储
    this.syncStore = new Store({
      name: 'calendar-sync',
      defaults: {
        syncedTodos: {},
        lastSyncTime: null
      }
    })

    // 初始化时加载同步记录
    this.loadSyncedTodosFromStore()

    this.setupIPC()
  }

  // 从持久化存储加载同步记录
  loadSyncedTodosFromStore() {
    try {
      const storedSyncedTodos = this.syncStore.get('syncedTodos', {})
      this.syncedTodos = new Map(Object.entries(storedSyncedTodos))
      console.log(`📅 [OUTLOOK_CALENDAR] 从存储加载了 ${this.syncedTodos.size} 个同步记录`)
    } catch (error) {
      console.error('📅 [OUTLOOK_CALENDAR] 加载同步记录失败:', error)
      this.syncedTodos = new Map()
    }
  }

  // 保存同步记录到持久化存储
  saveSyncedTodosToStore() {
    try {
      const syncedTodosObj = Object.fromEntries(this.syncedTodos)
      this.syncStore.set('syncedTodos', syncedTodosObj)
      this.syncStore.set('lastSyncTime', new Date().toISOString())
      console.log(`📅 [OUTLOOK_CALENDAR] 已保存 ${this.syncedTodos.size} 个同步记录到存储`)
    } catch (error) {
      console.error('📅 [OUTLOOK_CALENDAR] 保存同步记录失败:', error)
    }
  }

  setupIPC() {
    // 手动同步日历
    ipcMain.handle('sync-calendar-manual', async (event, todoIds) => {
      return await this.syncTodosToCalendar(todoIds)
    })

    // 获取同步状态
    ipcMain.handle('get-sync-status', async () => {
      return {
        success: true,
        syncedCount: this.syncedTodos.size,
        syncedTodos: Array.from(this.syncedTodos.values())
      }
    })
  }

  async initialize() {
    if (this.isInitialized) return

    console.log('📅 [OUTLOOK_CALENDAR] 初始化Outlook日历服务...')

    // 等待MCP服务初始化完成
    await this.waitForMCPReady()

    this.isInitialized = true
    console.log('📅 [OUTLOOK_CALENDAR] Outlook日历服务初始化完成')
  }

  async waitForMCPReady() {
    console.log('📅 [OUTLOOK_CALENDAR] 检查或初始化Outlook日历MCP服务...')

    // 先尝试按需初始化
    try {
      const outlookClient = await this.mcpManager.initializeOutlookCalendarMCP()
      if (outlookClient && outlookClient.isConnected) {
        console.log('📅 [OUTLOOK_CALENDAR] Outlook日历MCP服务已就绪（按需初始化）')
        return true
      }
    } catch (initError) {
      console.warn('📅 [OUTLOOK_CALENDAR] Outlook日历MCP按需初始化失败:', initError.message)
    }

    // 如果按需初始化失败，检查是否已有可用客户端
    const outlookClient = this.mcpManager.clients.get('outlook-calendar')
    if (outlookClient && outlookClient.isConnected) {
      console.log('📅 [OUTLOOK_CALENDAR] 找到现有的Outlook日历MCP客户端')
      return true
    }

    console.warn('📅 [OUTLOOK_CALENDAR] Outlook日历MCP服务不可用')
    console.warn('📅 [OUTLOOK_CALENDAR] 请确保Microsoft Outlook已安装并正在运行')
    return false
  }

  /**
   * 同步待办事项到Outlook日历
   */
  async syncTodosToCalendar(todoItems) {
    if (!Array.isArray(todoItems)) return { success: false, error: '待办事项数据格式错误' }

    console.log(`📅 [OUTLOOK_CALENDAR] 开始同步 ${todoItems.length} 个待办事项到Outlook日历`)

    const syncResults = []

    for (const todo of todoItems) {
      try {
        const result = await this.syncSingleTodo(todo)
        syncResults.push(result)

        if (result.success) {
          // 记录已同步的待办事项
          this.syncedTodos.set(todo.id, {
            todoId: todo.id,
            calendarEventId: result.eventId,
            syncTime: new Date().toISOString(),
            todo: todo
          })

          // 保存同步记录到持久化存储
          this.saveSyncedTodosToStore()
        }
      } catch (error) {
        console.error(`📅 [OUTLOOK_CALENDAR] 同步待办事项失败: ${todo.subject}`, error)
        syncResults.push({
          success: false,
          todoId: todo.id,
          error: error.message
        })
      }
    }

    const successCount = syncResults.filter(r => r.success).length
    console.log(`📅 [OUTLOOK_CALENDAR] 同步完成：成功 ${successCount}/${todoItems.length} 个`)

    return {
      success: true,
      totalCount: todoItems.length,
      successCount: successCount,
      results: syncResults
    }
  }

  /**
   * 同步单个待办事项到Outlook日历
   */
  async syncSingleTodo(todo) {
    try {
      // 构建日历事件参数
      const eventParams = this.buildCalendarEventParams(todo)

      console.log(`📅 [OUTLOOK_CALENDAR] 创建日历事件: ${todo.subject}`)

      // 调用MCP工具创建日历事件
      const result = await this.mcpManager.callRealMCPTool('create_event', eventParams)

      if (result.success) {
        console.log(`📅 [OUTLOOK_CALENDAR] 事件创建成功: ${todo.subject}`)
        return {
          success: true,
          todoId: todo.id,
          eventId: result.eventId || result.event_id,
          message: `待办事项 "${todo.subject}" 已同步到Outlook日历`
        }
      } else {
        console.error(`📅 [OUTLOOK_CALENDAR] 事件创建失败: ${todo.subject}`, result.error)
        return {
          success: false,
          todoId: todo.id,
          error: result.error || '创建日历事件失败'
        }
      }
    } catch (error) {
      console.error(`📅 [OUTLOOK_CALENDAR] 同步待办事项异常: ${todo.subject}`, error)
      return {
        success: false,
        todoId: todo.id,
        error: error.message
      }
    }
  }

  /**
   * 构建日历事件参数
   */
  buildCalendarEventParams(todo) {
    const now = new Date()

    // 根据待办事项类型设置不同的日程
    let startDateTime, endDateTime

    if (todo.dueDate) {
      // 如果有截止日期，设置为截止日期前的合适时间
      const dueTime = new Date(todo.dueDate)

      if (todo.todoType === 'meeting') {
        // 会议类型：设置为截止时间
        startDateTime = dueTime
        endDateTime = new Date(dueTime.getTime() + 60 * 60 * 1000) // 默认1小时
      } else {
        // 任务类型：设置为截止日期前1小时的提醒
        const reminderTime = new Date(dueTime.getTime() - 60 * 60 * 1000)
        startDateTime = reminderTime
        endDateTime = new Date(reminderTime.getTime() + 30 * 60 * 1000) // 30分钟提醒
      }
    } else {
      // 没有截止日期，设置为明天的合适时间
      const tomorrow = new Date(now.getTime() + 24 * 60 * 60 * 1000)

      if (todo.todoType === 'meeting') {
        // 会议设置为明天上午10点
        tomorrow.setHours(10, 0, 0, 0)
        startDateTime = tomorrow
        endDateTime = new Date(tomorrow.getTime() + 60 * 60 * 1000) // 1小时会议
      } else {
        // 任务设置为明天上午9点
        tomorrow.setHours(9, 0, 0, 0)
        startDateTime = tomorrow
        endDateTime = new Date(tomorrow.getTime() + 30 * 60 * 1000) // 30分钟提醒
      }
    }

    return {
      subject: `[待办] ${todo.subject}`,
      startDate: this.formatDate(startDateTime),
      startTime: this.formatTime(startDateTime),
      endDate: this.formatDate(endDateTime),
      endTime: this.formatTime(endDateTime),
      location: todo.location || '办公室',
      body: this.buildEventBody(todo),
      isMeeting: todo.todoType === 'meeting',
      attendees: todo.attendees || '',
      calendar: '' // 空字符串使用默认日历
    }
  }

  /**
   * 构建事件内容
   */
  buildEventBody(todo) {
    let body = `📧 待办事项同步\n\n`
    body += `📋 类型: ${this.getTodoTypeText(todo.todoType)}\n`
    body += `📝 描述: ${todo.todoDescription || '无描述'}\n`
    body += `📧 来源: ${todo.from || '未知'}\n`
    body += `⚡ 紧急程度: ${this.getUrgencyText(todo.urgency)}\n`

    if (todo.dueDate) {
      body += `⏰ 截止时间: ${new Date(todo.dueDate).toLocaleString('zh-CN')}\n`
    }

    body += `\n🤖 此事项由AI助手自动同步到日历`

    return body
  }

  /**
   * 格式化日期为 MM/DD/YYYY 格式
   */
  formatDate(date) {
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const year = date.getFullYear()
    return `${month}/${day}/${year}`
  }

  /**
   * 格式化时间为 HH:MM AM/PM 格式
   */
  formatTime(date) {
    return date.toLocaleString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    })
  }

  /**
   * 获取待办事项类型文本
   */
  getTodoTypeText(todoType) {
    const typeMap = {
      meeting: '📅 会议',
      task: '📋 任务',
      deadline: '⏰ 截止',
      reply: '📨 回复',
      approval: '✅ 审批'
    }
    return typeMap[todoType] || '📄 其他'
  }

  /**
   * 获取紧急程度文本
   */
  getUrgencyText(urgency) {
    const urgencyMap = {
      high: '🔴 紧急',
      medium: '🟡 普通',
      low: '🟢 较低'
    }
    return urgencyMap[urgency] || '🟡 普通'
  }

  /**
   * 获取同步状态
   */
  getSyncStatus() {
    return {
      isInitialized: this.isInitialized,
      syncedCount: this.syncedTodos.size,
      syncedTodos: Array.from(this.syncedTodos.values())
    }
  }

  /**
   * 清理服务
   */
  cleanup() {
    // 最后一次保存同步记录
    this.saveSyncedTodosToStore()
    this.syncedTodos.clear()
    console.log('📅 [OUTLOOK_CALENDAR] Outlook日历服务已清理')
  }
}

module.exports = OutlookCalendarService
