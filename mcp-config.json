{"mcpServers": {"office-bot-mcp": {"command": "python/py/python/python.exe", "args": ["mcp-servers/office-bot/main.py"], "env": {"PYTHONPATH": "python/py/python/Lib/site-packages"}}, "email-server-mcp": {"command": "python/py/python/python.exe", "args": ["mcp-servers/email-server/main.py"], "env": {"PYTHONPATH": "python/py/python/Lib/site-packages"}}, "weather-server-mcp": {"command": "python/py/python/python.exe", "args": ["mcp-servers/weather-server/src/main.py"], "env": {"PYTHONPATH": "python/py/python/Lib/site-packages"}}}}