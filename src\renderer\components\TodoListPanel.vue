<template>
  <div class="todo-panel">
    <div class="panel-header">
      <h3>📧 邮件待办事项</h3>
      <div class="header-actions">
        <button @click="refreshTodos" class="refresh-btn" :disabled="isRefreshing">
          {{ isRefreshing ? '刷新中...' : '刷新' }}
        </button>
        <button @click="checkEmailsManually" class="check-email-btn" :disabled="isChecking">
          {{ isChecking ? '检查中...' : '检查邮件' }}
        </button>
        <button @click="syncCalendarManually" class="sync-calendar-btn" :disabled="isSyncing">
          {{ isSyncing ? '同步中...' : '同步日历' }}
        </button>
      </div>
    </div>

    <div class="panel-body">
      <div v-if="todos.length === 0" class="empty-state">
        <div class="empty-icon">📭</div>
        <p>暂无待办事项</p>
        <p class="empty-hint">系统会自动检查邮件并提取待办事项</p>
      </div>

      <div v-else class="todo-list">
        <div 
          v-for="todo in todos" 
          :key="todo.id"
          class="todo-item"
          :class="{
            'high-urgency': todo.urgency === 'high',
            'medium-urgency': todo.urgency === 'medium',
            'low-urgency': todo.urgency === 'low',
            'completed': todo.completed
          }"
        >
          <div class="todo-header">
            <div class="todo-meta">
              <span class="todo-type">{{ getTodoTypeLabel(todo.todoType) }}</span>
              <span class="urgency-badge">{{ getUrgencyLabel(todo.urgency) }}</span>
            </div>
            <div class="todo-actions">
              <button 
                @click="toggleComplete(todo)"
                class="complete-btn"
                :class="{ active: todo.completed }"
              >
                {{ todo.completed ? '✓' : '○' }}
              </button>
            </div>
          </div>

          <div class="todo-content">
            <h4 class="todo-subject">{{ todo.subject }}</h4>
            <p class="todo-description">{{ todo.todoDescription }}</p>
                      <div class="todo-details">
            <span class="todo-from">来自: {{ todo.from }}</span>
            <span v-if="todo.dueDate" class="todo-due">
              截止: {{ formatDate(todo.dueDate) }}
            </span>
            <span v-if="todo.syncedToCalendar" class="sync-status synced">
              📅 已同步到日历
            </span>
            <span v-else class="sync-status not-synced">
              ⏳ 未同步
            </span>
          </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 提醒弹框 -->
    <div v-if="showReminder" class="reminder-modal" @click="closeReminder">
      <div class="reminder-content" @click.stop>
        <div class="reminder-header">
          <h3>⏰ 待办事项提醒</h3>
          <button @click="closeReminder" class="close-btn">×</button>
        </div>
        
        <div class="reminder-body">
          <h4>{{ reminderData.subject }}</h4>
          <p class="reminder-description">{{ reminderData.todoDescription }}</p>
          <div class="reminder-meta">
            <span class="reminder-from">来自: {{ reminderData.from }}</span>
            <span v-if="reminderData.dueDate" class="reminder-due">
              截止时间: {{ formatDate(reminderData.dueDate) }}
            </span>
          </div>
        </div>

        <div class="reminder-footer">
          <button @click="snoozeReminder" class="snooze-btn">稍后提醒</button>
          <button @click="markAsCompleted" class="complete-btn">标记完成</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TodoListPanel',

  data() {
    return {
      todos: [],
      isRefreshing: false,
      isChecking: false,
      isSyncing: false,
      showReminder: false,
      reminderData: {},
      emailStats: {
        totalEmails: 0,
        todoEmails: 0
      },
      syncStatus: {
        syncedCount: 0,
        syncedTodos: []
      }
    }
  },

  mounted() {
    this.loadTodos()
    this.loadSyncStatus()
    this.setupEventListeners()
  },

  beforeUnmount() {
    this.removeEventListeners()
  },

  methods: {
    async loadTodos() {
      try {
        this.isRefreshing = true
        const result = await window.electronAPI.invoke('get-todo-list')
        if (result.success) {
          this.todos = result.todos || []
        }
      } catch (error) {
        console.error('加载待办列表失败:', error)
      } finally {
        this.isRefreshing = false
      }
    },

    async refreshTodos() {
      await this.loadTodos()
    },

    async checkEmailsManually() {
      try {
        this.isChecking = true
        const result = await window.electronAPI.checkEmailsManual()
        if (result.success) {
          this.$emit('status-update', result.message)
          await this.loadTodos()
        } else {
          this.$emit('error', '检查邮件失败: ' + result.error)
        }
      } catch (error) {
        console.error('手动检查邮件失败:', error)
        this.$emit('error', '检查邮件时出错: ' + error.message)
      } finally {
        this.isChecking = false
      }
    },

    async syncCalendarManually() {
      try {
        this.isSyncing = true
        
        // 获取所有未同步的待办事项
        const unsyncedTodos = this.todos.filter(todo => !todo.syncedToCalendar)
        
        if (unsyncedTodos.length === 0) {
          this.$emit('status-update', '所有待办事项已同步到日历')
          return
        }
        
        const result = await window.electronAPI.syncCalendarManual(unsyncedTodos)
        
        if (result.success) {
          this.$emit('status-update', `成功同步 ${result.successCount}/${result.totalCount} 个待办事项到日历`)
          await this.loadTodos()
          await this.loadSyncStatus()
        } else {
          this.$emit('error', '同步日历失败: ' + result.error)
        }
      } catch (error) {
        console.error('手动同步日历失败:', error)
        this.$emit('error', '同步日历时出错: ' + error.message)
      } finally {
        this.isSyncing = false
      }
    },

    async loadSyncStatus() {
      try {
        const result = await window.electronAPI.getSyncStatus()
        if (result.success) {
          this.syncStatus = {
            syncedCount: result.syncedCount,
            syncedTodos: result.syncedTodos
          }
        }
      } catch (error) {
        console.error('加载同步状态失败:', error)
      }
    },

    toggleComplete(todo) {
      todo.completed = !todo.completed
      // 这里可以调用后端API保存状态
      this.$emit('todo-updated', todo)
    },

    getTodoTypeLabel(type) {
      const typeLabels = {
        meeting: '📅 会议',
        task: '📋 任务',
        deadline: '⏰ 截止',
        reply: '📨 回复',
        approval: '✅ 审批'
      }
      return typeLabels[type] || '📄 其他'
    },

    getUrgencyLabel(urgency) {
      const urgencyLabels = {
        high: '🔴 紧急',
        medium: '🟡 普通',
        low: '🟢 较低'
      }
      return urgencyLabels[urgency] || '🟡 普通'
    },

    formatDate(dateString) {
      if (!dateString) return ''
      try {
        const date = new Date(dateString)
        return date.toLocaleString('zh-CN', {
          month: 'short',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit'
        })
      } catch (error) {
        return dateString
      }
    },

    setupEventListeners() {
      // 监听邮件处理完成事件
      window.electronAPI.onNewEmailsProcessed?.((data) => {
        this.emailStats = {
          totalEmails: data.totalEmails,
          todoEmails: data.todoEmails
        }
        this.loadTodos()
        this.$emit('status-update', `处理了 ${data.totalEmails} 封邮件，新增 ${data.todoEmails} 个待办事项`)
      })

      // 监听邮件提醒事件
      window.electronAPI.onShowEmailReminder?.((reminderData) => {
        this.reminderData = reminderData
        this.showReminder = true
      })

      // 监听待办事项同步到日历的事件
      window.electronAPI.onTodoSyncedToCalendar?.((data) => {
        if (data.success) {
          this.$emit('status-update', `待办事项 "${data.subject}" 已同步到Outlook日历`)
          // 更新对应的待办事项状态
          const todo = this.todos.find(t => t.id === data.todoId)
          if (todo) {
            todo.syncedToCalendar = true
            todo.calendarEventId = data.eventId
          }
        }
        this.loadSyncStatus()
      })
    },

    removeEventListeners() {
      // 移除事件监听器（如果有提供移除方法）
      if (window.electronAPI.removeNewEmailsProcessedListener) {
        window.electronAPI.removeNewEmailsProcessedListener()
      }
      if (window.electronAPI.removeShowEmailReminderListener) {
        window.electronAPI.removeShowEmailReminderListener()
      }
    },

    closeReminder() {
      this.showReminder = false
      this.reminderData = {}
    },

    snoozeReminder() {
      // 稍后提醒（5分钟后）
      this.closeReminder()
      this.$emit('status-update', '提醒已推迟5分钟')
    },

    async markAsCompleted() {
      try {
        // 将对应的待办事项标记为完成
        const todo = this.todos.find(t => t.id === this.reminderData.id)
        if (todo) {
          this.toggleComplete(todo)
        }
        
        // 清除提醒
        await window.electronAPI.invoke('clear-reminder', this.reminderData.id)
        this.closeReminder()
        this.$emit('status-update', '待办事项已完成')
      } catch (error) {
        console.error('标记完成失败:', error)
        this.$emit('error', '标记完成失败: ' + error.message)
      }
    }
  }
}
</script>

<style scoped>
.todo-panel {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  max-height: 600px;
  display: flex;
  flex-direction: column;
}

.panel-header {
  background: #f8f9fa;
  padding: 16px 20px;
  border-bottom: 1px solid #e1e5e9;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.panel-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 16px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.refresh-btn, .check-email-btn, .sync-calendar-btn {
  padding: 6px 12px;
  border: 1px solid #ddd;
  background: white;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s;
}

.refresh-btn:hover, .check-email-btn:hover, .sync-calendar-btn:hover {
  background: #f5f5f5;
}

.refresh-btn:disabled, .check-email-btn:disabled, .sync-calendar-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.sync-calendar-btn {
  background: #4285f4;
  color: white;
  border-color: #4285f4;
}

.sync-calendar-btn:hover:not(:disabled) {
  background: #3367d6;
}

.panel-body {
  flex: 1;
  overflow-y: auto;
  padding: 16px 20px;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #6c757d;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.empty-hint {
  font-size: 14px;
  color: #999;
}

.todo-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.todo-item {
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  padding: 16px;
  transition: all 0.2s;
}

.todo-item.high-urgency {
  border-color: #dc3545;
  background: #fff5f5;
}

.todo-item.medium-urgency {
  border-color: #ffc107;
  background: #fffdf5;
}

.todo-item.low-urgency {
  border-color: #28a745;
  background: #f5fff5;
}

.todo-item.completed {
  opacity: 0.6;
  text-decoration: line-through;
}

.todo-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.todo-meta {
  display: flex;
  gap: 8px;
}

.todo-type, .urgency-badge {
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 4px;
  background: #f8f9fa;
  color: #495057;
}

.urgency-badge {
  font-weight: 600;
}

.complete-btn {
  width: 24px;
  height: 24px;
  border: 2px solid #ddd;
  border-radius: 50%;
  background: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  transition: all 0.2s;
}

.complete-btn.active {
  background: #28a745;
  border-color: #28a745;
  color: white;
}

.todo-subject {
  margin: 0 0 8px 0;
  color: #2c3e50;
  font-size: 14px;
  font-weight: 600;
}

.todo-description {
  margin: 0 0 12px 0;
  color: #495057;
  font-size: 13px;
  line-height: 1.4;
}

.todo-details {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #6c757d;
}

.todo-from, .todo-due, .sync-status {
  font-size: 12px;
}

.todo-due {
  font-weight: 600;
}

.sync-status {
  font-weight: 500;
  border-radius: 4px;
  padding: 2px 6px;
}

.sync-status.synced {
  background: #e8f5e8;
  color: #28a745;
  border: 1px solid #d4edda;
}

.sync-status.not-synced {
  background: #fff3cd;
  color: #856404;
  border: 1px solid #ffeaa7;
}

/* 提醒弹框样式 */
.reminder-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  backdrop-filter: blur(4px);
}

.reminder-content {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 500px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  overflow: hidden;
}

.reminder-header {
  background: #fff3cd;
  padding: 16px 20px;
  border-bottom: 1px solid #ffeaa7;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.reminder-header h3 {
  margin: 0;
  color: #856404;
  font-size: 16px;
}

.close-btn {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: #856404;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.reminder-body {
  padding: 20px;
}

.reminder-body h4 {
  margin: 0 0 12px 0;
  color: #2c3e50;
  font-size: 16px;
}

.reminder-description {
  margin: 0 0 16px 0;
  color: #495057;
  line-height: 1.5;
}

.reminder-meta {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
  color: #6c757d;
}

.reminder-footer {
  padding: 16px 20px;
  background: #f8f9fa;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.snooze-btn, .reminder-footer .complete-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.snooze-btn {
  background: #6c757d;
  color: white;
}

.snooze-btn:hover {
  background: #5a6268;
}

.reminder-footer .complete-btn {
  background: #28a745;
  color: white;
}

.reminder-footer .complete-btn:hover {
  background: #218838;
}
</style> 