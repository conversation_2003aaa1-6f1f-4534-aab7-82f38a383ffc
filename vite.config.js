import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import electron from 'vite-plugin-electron'
import renderer from 'vite-plugin-electron-renderer'
import { resolve, dirname } from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

export default defineConfig({
  plugins: [
    vue(),
    electron([
      {
        entry: resolve(__dirname, 'src/main/main.js'),
        vite: {
          build: {
            rollupOptions: {
              external: [
                'electron',
                '@libsql/client',
                '@libsql/win32-x64-msvc',
                '@libsql/linux-x64-gnu',
                '@libsql/darwin-x64',
                '@libsql/darwin-arm64',
                'openai',
                'mammoth',
                'turndown',
                'electron-store',
                'axios'
              ]
            }
          }
        }
      },
      {
        entry: resolve(__dirname, 'src/preload/preload.js'),
        onstart(options) {
          options.reload()
        },
        vite: {
          build: {
            rollupOptions: {
              external: ['electron']
            }
          }
        }
      }
    ]),
    renderer()
  ],
  root: 'src/renderer',
  base: './',
  build: {
    outDir: '../../dist',
    emptyOutDir: false,
    // 确保utils文件夹被复制到输出目录
    copyPublicDir: true,
    assetsDir: 'assets',
    rollupOptions: {
      input: {
        main: resolve(__dirname, 'src/renderer/index.html'),
        floating: resolve(__dirname, 'src/renderer/floating.html')
      },
      output: {
        // 确保utils目录结构被保持
        assetFileNames: (assetInfo) => {
          if (assetInfo.name && assetInfo.name.includes('cryptojs')) {
            return 'utils/[name][extname]'
          }
          return 'assets/[name]-[hash][extname]'
        }
      }
    }
  },
  css: {
    preprocessorOptions: {
      scss: {
        // 暂时移除自动导入，避免路径错误
        // additionalData: `@import "${resolve(__dirname, 'src/renderer/assets/styles/_variables.scss')}";`
      }
    }
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src/renderer')
    }
  },
  server: {
    port: 6913,
    host: 'localhost'
  },
  publicDir: resolve(__dirname, 'public')
}) 