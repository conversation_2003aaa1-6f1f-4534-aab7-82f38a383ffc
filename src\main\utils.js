// === Console 转发设置 ===

// 保存原始console方法
const originalConsole = {
  log: console.log,
  warn: console.warn,
  error: console.error,
  info: console.info,
  debug: console.debug
}

// 重写console方法，转发到浏览器控制台
function setupConsoleForwarding(mainWindow) {
  const forwardConsole = (level, originalMethod) => {
    return (...args) => {
      // 调用原始方法，保持终端输出
      originalMethod.apply(console, args)

      // 转发到浏览器控制台
      if (mainWindow && !mainWindow.isDestroyed()) {
        try {
          // 将参数序列化为字符串
          const message = args.map(arg => {
            if (typeof arg === 'object') {
              try {
                return JSON.stringify(arg, null, 2)
              } catch (e) {
                return String(arg)
              }
            }
            return String(arg)
          }).join(' ')

          mainWindow.webContents.send('main-console-log', {
            level,
            message,
            timestamp: new Date().toISOString()
          })
        } catch (error) {
          // 避免转发过程中出现循环错误
          originalConsole.error('Console转发失败:', error.message)
        }
      }
    }
  }

  console.log = forwardConsole('log', originalConsole.log)
  console.warn = forwardConsole('warn', originalConsole.warn)
  console.error = forwardConsole('error', originalConsole.error)
  console.info = forwardConsole('info', originalConsole.info)
  console.debug = forwardConsole('debug', originalConsole.debug)
}

// === Windows子进程窗口隐藏设置 ===

function setupWindowsSpawnInterception() {
  // 在Windows上全局隐藏子进程窗口 - 彻底拦截所有spawn调用
  if (process.platform === 'win32') {
    process.env.PYTHONUNBUFFERED = '1'
    process.env.PYTHONIOENCODING = 'utf-8'

    // 彻底覆盖child_process模块的spawn方法
    const childProcess = require('child_process')
    const originalSpawn = childProcess.spawn

    // 覆盖spawn方法 - 确保所有调用都被拦截
    childProcess.spawn = function (...args) {
      console.log('🔧 拦截spawn调用:', args[0], args[1]?.slice(0, 2) || [])

      if (args[2] && typeof args[2] === 'object') {
        // 确保所有必要的窗口隐藏选项都被设置
        args[2].windowsHide = true
        args[2].shell = false
        args[2].detached = false
        args[2].windowsVerbatimArguments = false
        // 强制使用pipe模式，不继承任何输出流
        args[2].stdio = ['pipe', 'pipe', 'pipe']
        args[2].flags = 0x08000000 // CREATE_NO_WINDOW flag
      } else if (!args[2]) {
        args[2] = {
          windowsHide: true,
          shell: false,
          detached: false,
          windowsVerbatimArguments: false,
          stdio: ['pipe', 'pipe', 'pipe'],
          flags: 0x08000000 // CREATE_NO_WINDOW flag
        }
      }

      console.log('🔧 spawn配置 (强制pipe模式):', {
        windowsHide: args[2].windowsHide,
        stdio: args[2].stdio,
        flags: args[2].flags,
        shell: args[2].shell
      })
      return originalSpawn.apply(this, args)
    }

    // 同时覆盖模块缓存中的spawn - 确保SDK也使用我们的版本
    const Module = require('module')
    const originalRequire = Module.prototype.require
    Module.prototype.require = function (id) {
      const module = originalRequire.apply(this, arguments)
      if (id === 'child_process' && module.spawn !== childProcess.spawn) {
        console.log('🔧 修复child_process模块的spawn方法')
        module.spawn = childProcess.spawn
      }
      return module
    }
  }
}

// === 知识库配置管理 ===

// 默认知识库配置（主进程使用）
const DEFAULT_KNOWLEDGE_CONFIG = {
  database: {
    url: null, // 将在初始化时设置
    timeout: 30000
  },
  embedding: {
    baseURL: 'https://api.siliconflow.cn/v1',
    apiKey: 'sk-cubtiycbsbczznzdpxfwsomvhnnpzoikstnubshkpimhqhzy',
    model: 'BAAI/bge-m3',
    encoding_format: 'float',
    maxTokens: 8000 // API支持的最大token数
  },
  document: {
    minSplitLength: 500,     // 最小分割长度 (降低到500字符)
    maxSplitLength: 1000,    // 最大分割长度 (降低到1000字符，避免API限制)
    supportedFormats: ['.txt', '.md', '.docx', '.doc']
  },
  search: {
    defaultLimit: 4,          // 降低默认返回数量，确保质量
    similarityThreshold: 0.4,  // 提高相似度阈值到50%，确保相关性
    minSimilarityThreshold: 0.4, // 最低相似度阈值，低于此值直接排除
    maxResultsPerDocument: 2  // 每个文档最多返回2个片段
  }
}

// 从渲染进程获取最新配置的函数
function getKnowledgeConfig() {
  // 优先使用渲染进程的配置，如果没有则使用默认配置
  return global.sharedKnowledgeConfig || DEFAULT_KNOWLEDGE_CONFIG
}

// 更新知识库配置
function updateKnowledgeConfig(newConfig) {
  if (global.sharedKnowledgeConfig) {
    global.sharedKnowledgeConfig = { ...global.sharedKnowledgeConfig, ...newConfig }
  } else {
    global.sharedKnowledgeConfig = { ...DEFAULT_KNOWLEDGE_CONFIG, ...newConfig }
  }
  
  console.log('🧠 知识库配置已更新:', global.sharedKnowledgeConfig)
}

module.exports = {
  originalConsole,
  setupConsoleForwarding,
  setupWindowsSpawnInterception,
  DEFAULT_KNOWLEDGE_CONFIG,
  getKnowledgeConfig,
  updateKnowledgeConfig
}
